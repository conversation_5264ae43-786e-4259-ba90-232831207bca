using System;
using System.Threading;
using System.Threading.Tasks;
using SystemCleanerPro.Models;

namespace SystemCleanerPro.Services
{
    /// <summary>
    /// خدمة الجدولة التلقائية
    /// </summary>
    public class SchedulerService
    {
        private Timer? _scheduledTimer;
        private readonly TempFileCleanerService _tempFileCleaner;
        private readonly MemoryOptimizerService _memoryOptimizer;
        private ScheduleSettings _settings;

        public event EventHandler<CleaningResult>? ScheduledCleaningCompleted;
        public event EventHandler<string>? StatusUpdated;

        public SchedulerService(TempFileCleanerService tempFileCleaner, MemoryOptimizerService memoryOptimizer)
        {
            _tempFileCleaner = tempFileCleaner;
            _memoryOptimizer = memoryOptimizer;
            _settings = new ScheduleSettings();
        }

        public void StartScheduledCleaning(ScheduleSettings settings)
        {
            _settings = settings;
            
            if (!_settings.IsEnabled)
            {
                StopScheduledCleaning();
                return;
            }

            var interval = CalculateInterval(settings.Frequency, settings.CustomInterval);
            
            _scheduledTimer = new Timer(async _ => await PerformScheduledCleaningAsync(), 
                                      null, 
                                      interval, 
                                      interval);

            StatusUpdated?.Invoke(this, $"تم تفعيل التنظيف التلقائي كل {GetFrequencyDescription(settings.Frequency)}");
        }

        public void StopScheduledCleaning()
        {
            _scheduledTimer?.Dispose();
            _scheduledTimer = null;
            StatusUpdated?.Invoke(this, "تم إيقاف التنظيف التلقائي");
        }

        private TimeSpan CalculateInterval(CleaningFrequency frequency, int customInterval)
        {
            return frequency switch
            {
                CleaningFrequency.Every5Minutes => TimeSpan.FromMinutes(5),
                CleaningFrequency.Every30Minutes => TimeSpan.FromMinutes(30),
                CleaningFrequency.Hourly => TimeSpan.FromHours(1),
                CleaningFrequency.Daily => TimeSpan.FromDays(1),
                CleaningFrequency.Weekly => TimeSpan.FromDays(7),
                CleaningFrequency.Custom => TimeSpan.FromMinutes(customInterval),
                _ => TimeSpan.FromHours(1)
            };
        }

        private string GetFrequencyDescription(CleaningFrequency frequency)
        {
            return frequency switch
            {
                CleaningFrequency.Every5Minutes => "5 دقائق",
                CleaningFrequency.Every30Minutes => "30 دقيقة",
                CleaningFrequency.Hourly => "ساعة",
                CleaningFrequency.Daily => "يوم",
                CleaningFrequency.Weekly => "أسبوع",
                CleaningFrequency.Custom => $"{_settings.CustomInterval} دقيقة",
                _ => "ساعة"
            };
        }

        private async Task PerformScheduledCleaningAsync()
        {
            try
            {
                StatusUpdated?.Invoke(this, "بدء التنظيف التلقائي...");

                var combinedResult = new CleaningResult
                {
                    CleaningType = "تنظيف تلقائي مجدول",
                    CleaningDate = DateTime.Now
                };

                var startTime = DateTime.Now;

                // تنظيف الملفات المؤقتة إذا كان مفعلاً
                if (_settings.CleanTempFiles)
                {
                    var tempResult = await _tempFileCleaner.CleanTempFilesAsync();
                    combinedResult.TotalFilesDeleted += tempResult.TotalFilesDeleted;
                    combinedResult.TotalSpaceFreed += tempResult.TotalSpaceFreed;
                    combinedResult.CleanedLocations.AddRange(tempResult.CleanedLocations);
                    combinedResult.Errors.AddRange(tempResult.Errors);
                }

                // تحسين الذاكرة إذا كان مفعلاً
                if (_settings.OptimizeMemory)
                {
                    var memoryResult = await _memoryOptimizer.OptimizeMemoryAsync();
                    combinedResult.TotalSpaceFreed += memoryResult.TotalSpaceFreed;
                    combinedResult.CleanedLocations.AddRange(memoryResult.CleanedLocations);
                    combinedResult.Errors.AddRange(memoryResult.Errors);
                }

                combinedResult.CleaningDuration = DateTime.Now - startTime;
                combinedResult.IsSuccessful = combinedResult.Errors.Count == 0;

                ScheduledCleaningCompleted?.Invoke(this, combinedResult);
                
                var message = combinedResult.IsSuccessful 
                    ? $"تم التنظيف التلقائي بنجاح - تحرير {combinedResult.GetFormattedSpaceFreed()}"
                    : "فشل في التنظيف التلقائي";
                
                StatusUpdated?.Invoke(this, message);
            }
            catch (Exception ex)
            {
                StatusUpdated?.Invoke(this, $"خطأ في التنظيف التلقائي: {ex.Message}");
            }
        }

        public bool IsScheduledCleaningActive => _scheduledTimer != null;

        public ScheduleSettings GetCurrentSettings()
        {
            return _settings;
        }

        public void Dispose()
        {
            StopScheduledCleaning();
        }
    }

    /// <summary>
    /// إعدادات الجدولة
    /// </summary>
    public class ScheduleSettings
    {
        public bool IsEnabled { get; set; } = false;
        public CleaningFrequency Frequency { get; set; } = CleaningFrequency.Daily;
        public int CustomInterval { get; set; } = 60; // بالدقائق
        public bool CleanTempFiles { get; set; } = true;
        public bool OptimizeMemory { get; set; } = true;
        public bool CleanRegistry { get; set; } = false;
        public bool SilentMode { get; set; } = true;
        public DateTime LastCleaningTime { get; set; } = DateTime.MinValue;
    }

    /// <summary>
    /// تكرار التنظيف
    /// </summary>
    public enum CleaningFrequency
    {
        Every5Minutes,
        Every30Minutes,
        Hourly,
        Daily,
        Weekly,
        Custom
    }
}
