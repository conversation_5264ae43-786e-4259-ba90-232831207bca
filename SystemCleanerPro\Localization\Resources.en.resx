<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  
  <!-- English Texts -->
  <data name="AppTitle" xml:space="preserve">
    <value>System Cleaner Pro</value>
  </data>
  <data name="QuickClean" xml:space="preserve">
    <value>Quick Clean</value>
  </data>
  <data name="CleanTempFiles" xml:space="preserve">
    <value>Clean Temp Files</value>
  </data>
  <data name="OptimizeMemory" xml:space="preserve">
    <value>Optimize Memory</value>
  </data>
  <data name="CleanRegistry" xml:space="preserve">
    <value>Clean Registry</value>
  </data>
  <data name="StartupManager" xml:space="preserve">
    <value>Startup Manager</value>
  </data>
  <data name="FindDuplicates" xml:space="preserve">
    <value>Find Duplicates</value>
  </data>
  <data name="CleanBrowsers" xml:space="preserve">
    <value>Clean Browsers</value>
  </data>
  <data name="DiskAnalyzer" xml:space="preserve">
    <value>Disk Analyzer</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="CPU" xml:space="preserve">
    <value>CPU</value>
  </data>
  <data name="Memory" xml:space="preserve">
    <value>Memory</value>
  </data>
  <data name="SystemScore" xml:space="preserve">
    <value>System Score</value>
  </data>
  <data name="SystemStatus" xml:space="preserve">
    <value>System Status</value>
  </data>
  <data name="CleaningReports" xml:space="preserve">
    <value>Cleaning Reports</value>
  </data>
  <data name="Ready" xml:space="preserve">
    <value>Ready</value>
  </data>
  <data name="Processing" xml:space="preserve">
    <value>Processing...</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="Failed" xml:space="preserve">
    <value>Failed</value>
  </data>
  <data name="FilesDeleted" xml:space="preserve">
    <value>Files Deleted</value>
  </data>
  <data name="SpaceFreed" xml:space="preserve">
    <value>Space Freed</value>
  </data>
  <data name="GeneralSettings" xml:space="preserve">
    <value>General Settings</value>
  </data>
  <data name="AutoScheduling" xml:space="preserve">
    <value>Auto Scheduling</value>
  </data>
  <data name="Alerts" xml:space="preserve">
    <value>Alerts and Monitoring</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="Theme" xml:space="preserve">
    <value>Theme</value>
  </data>
  <data name="StartWithWindows" xml:space="preserve">
    <value>Start with Windows</value>
  </data>
  <data name="MinimizeToTray" xml:space="preserve">
    <value>Minimize to Tray</value>
  </data>
  <data name="ShowNotifications" xml:space="preserve">
    <value>Show Notifications</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="RestoreDefaults" xml:space="preserve">
    <value>Restore Defaults</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>Enable</value>
  </data>
  <data name="Disable" xml:space="preserve">
    <value>Disable</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Publisher" xml:space="preserve">
    <value>Publisher</value>
  </data>
  <data name="Path" xml:space="preserve">
    <value>Path</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="RefreshList" xml:space="preserve">
    <value>Refresh List</value>
  </data>
  <data name="EnableSelected" xml:space="preserve">
    <value>Enable Selected</value>
  </data>
  <data name="DisableSelected" xml:space="preserve">
    <value>Disable Selected</value>
  </data>
</root>
