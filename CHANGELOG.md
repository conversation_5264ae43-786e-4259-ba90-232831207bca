# سجل التغييرات - Changelog

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-12-07

### ✨ المضاف - Added
- **واجهة المستخدم الرئيسية**: تصميم Material Design عصري مع دعم العربية والإنجليزية
- **تنظيف الملفات المؤقتة**: حذف ملفات Temp وملفات السجل من جميع المواقع
- **تحسين الذاكرة**: تحرير الرام باستخدام EmptyWorkingSet() API
- **تنظيف السجل**: حذف المفاتيح التالفة مع إنشاء نقاط استعادة تلقائية
- **مراقبة الأداء**: عرض حي لاستهلاك المعالج والذاكرة
- **نظام التقييم**: تقييم حالة النظام من 0 إلى 100
- **مدير برامج الإقلاع**: إدارة البرامج التي تعمل عند بدء التشغيل
- **باحث الملفات المكررة**: العثور على الملفات المكررة باستخدام Hash comparison
- **تنظيف المتصفحات**: دعم Chrome, Edge, Firefox, Opera
- **محلل القرص الصلب**: تحليل استخدام المساحة مع رسوم بيانية
- **الجدولة التلقائية**: تنظيف مجدول مع الوضع الصامت
- **نظام الإعدادات**: تخصيص شامل للبرنامج
- **التقارير المفصلة**: عرض نتائج التنظيف مع الإحصائيات
- **دعم متعدد اللغات**: العربية والإنجليزية
- **الثيمات**: وضع فاتح وداكن
- **التنبيهات الذكية**: إشعارات عند تجاوز نسب الاستهلاك

### 🛡️ الأمان - Security
- **صلاحيات المدير**: طلب صلاحيات UAC للوصول الآمن
- **حماية ملفات النظام**: تجنب الملفات والمجلدات الحرجة
- **نقاط الاستعادة**: إنشاء تلقائي قبل تنظيف السجل
- **التحقق من الملفات**: فحص صحة الملفات قبل الحذف

### 🔧 التقنيات - Technical
- **المنصة**: .NET 8.0 مع WPF
- **التصميم**: Material Design مع MaterialDesignThemes
- **المعمارية**: MVVM Pattern
- **الرسوم البيانية**: LiveCharts.Wpf
- **إدارة النظام**: System.Management APIs
- **السجل**: Microsoft.Win32.Registry

### 📋 المتطلبات - Requirements
- **نظام التشغيل**: Windows 7 أو أحدث
- **إطار العمل**: .NET 8.0 Runtime
- **الذاكرة**: 512 MB RAM كحد أدنى
- **المساحة**: 100 MB مساحة فارغة
- **الصلاحيات**: صلاحيات المدير

### 🐛 الإصلاحات - Fixed
- إصلاح مشكلة تعليق البرنامج أثناء تنظيف الملفات الكبيرة
- إصلاح خطأ في حساب المساحة المحررة
- إصلاح مشكلة عدم ظهور بعض برامج الإقلاع
- إصلاح خطأ في تحديث مؤشرات الأداء

### ⚠️ المعروف - Known Issues
- قد تظهر تحذيرات من مكافح الفيروسات (false positive)
- بعض الملفات المحمية قد لا يتم حذفها
- قد يتطلب إعادة تشغيل بعد تنظيف السجل

## [المخطط للإصدارات القادمة] - Planned

### 🔮 الإصدار 1.1.0 - مخطط
- **تحديث تلقائي**: نظام تحديث البرنامج تلقائياً
- **تنظيف متقدم**: أدوات تنظيف إضافية
- **تقارير مفصلة**: تصدير التقارير بصيغ مختلفة
- **دعم لغات إضافية**: الفرنسية والألمانية
- **تحسينات الأداء**: تسريع عمليات التنظيف

### 🔮 الإصدار 1.2.0 - مخطط
- **واجهة ويب**: لوحة تحكم عبر المتصفح
- **الشبكة**: إدارة أجهزة متعددة
- **الذكاء الاصطناعي**: تحليل ذكي للنظام
- **التكامل السحابي**: نسخ احتياطي سحابي

### 🔮 الإصدار 2.0.0 - مخطط
- **إعادة كتابة كاملة**: تحسين الأداء والاستقرار
- **واجهة جديدة**: تصميم أكثر حداثة
- **ميزات متقدمة**: أدوات احترافية للمطورين
- **دعم منصات أخرى**: Linux و macOS

---

## تنسيق سجل التغييرات

### أنواع التغييرات
- `Added` للميزات الجديدة
- `Changed` للتغييرات في الوظائف الموجودة
- `Deprecated` للميزات التي ستُزال قريباً
- `Removed` للميزات المُزالة
- `Fixed` لإصلاح الأخطاء
- `Security` للتحديثات الأمنية

### تنسيق الإصدارات
- `MAJOR.MINOR.PATCH` (مثال: 1.0.0)
- `MAJOR`: تغييرات غير متوافقة مع الإصدارات السابقة
- `MINOR`: إضافة وظائف جديدة متوافقة
- `PATCH`: إصلاح أخطاء متوافق

---

**ملاحظة**: هذا سجل التغييرات للإصدار الأول من البرنامج. سيتم تحديثه مع كل إصدار جديد.
