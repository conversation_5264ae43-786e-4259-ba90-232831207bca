using System;
using System.Collections.Generic;

namespace SystemCleanerPro.Models
{
    /// <summary>
    /// نموذج نتائج التنظيف
    /// </summary>
    public class CleaningResult
    {
        public DateTime CleaningDate { get; set; } = DateTime.Now;
        public long TotalFilesDeleted { get; set; }
        public long TotalSpaceFreed { get; set; }
        public TimeSpan CleaningDuration { get; set; }
        public List<string> CleanedLocations { get; set; } = new List<string>();
        public List<string> Errors { get; set; } = new List<string>();
        public bool IsSuccessful { get; set; }
        public string CleaningType { get; set; } = string.Empty;
        public int SystemScoreBefore { get; set; }
        public int SystemScoreAfter { get; set; }

        public string GetFormattedSpaceFreed()
        {
            if (TotalSpaceFreed < 1024)
                return $"{TotalSpaceFreed} B";
            else if (TotalSpaceFreed < 1024 * 1024)
                return $"{TotalSpaceFreed / 1024.0:F2} KB";
            else if (TotalSpaceFreed < 1024 * 1024 * 1024)
                return $"{TotalSpaceFreed / (1024.0 * 1024.0):F2} MB";
            else
                return $"{TotalSpaceFreed / (1024.0 * 1024.0 * 1024.0):F2} GB";
        }

        public string GetFormattedDuration()
        {
            if (CleaningDuration.TotalSeconds < 60)
                return $"{CleaningDuration.TotalSeconds:F1} ثانية";
            else if (CleaningDuration.TotalMinutes < 60)
                return $"{CleaningDuration.TotalMinutes:F1} دقيقة";
            else
                return $"{CleaningDuration.TotalHours:F1} ساعة";
        }
    }
}
