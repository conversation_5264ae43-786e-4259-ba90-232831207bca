#!/bin/bash
# Build Script for System Cleaner Pro
# سكريبت البناء لمنظف النظام الاحترافي

# Default values
CONFIGURATION="Release"
RUNTIME="win-x64"
SELF_CONTAINED=true
SINGLE_FILE=false
CLEAN=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_color() {
    printf "${1}${2}${NC}\n"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--configuration)
            CONFIGURATION="$2"
            shift 2
            ;;
        -r|--runtime)
            RUNTIME="$2"
            shift 2
            ;;
        --no-self-contained)
            SELF_CONTAINED=false
            shift
            ;;
        --single-file)
            SINGLE_FILE=true
            shift
            ;;
        --clean)
            CLEAN=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -c, --configuration    Build configuration (Debug/Release)"
            echo "  -r, --runtime         Target runtime (win-x64, linux-x64, osx-x64)"
            echo "  --no-self-contained   Create framework-dependent deployment"
            echo "  --single-file         Publish as single file"
            echo "  --clean               Clean previous builds"
            echo "  -h, --help            Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_color $CYAN "=== System Cleaner Pro Build Script ==="
print_color $YELLOW "Configuration: $CONFIGURATION"
print_color $YELLOW "Runtime: $RUNTIME"
print_color $YELLOW "Self-Contained: $SELF_CONTAINED"
print_color $YELLOW "Single File: $SINGLE_FILE"

# Clean previous builds
if [ "$CLEAN" = true ]; then
    print_color $GREEN "Cleaning previous builds..."
    rm -rf ./SystemCleanerPro/bin
    rm -rf ./SystemCleanerPro/obj
    rm -rf ./publish
fi

# Check for .NET SDK
print_color $GREEN "Checking .NET SDK..."
if ! command -v dotnet &> /dev/null; then
    print_color $RED "Error: .NET SDK not found. Please install .NET 8.0 SDK."
    exit 1
fi

DOTNET_VERSION=$(dotnet --version)
print_color $GREEN ".NET SDK Version: $DOTNET_VERSION"

# Navigate to project directory
cd SystemCleanerPro

# Restore packages
print_color $GREEN "Restoring packages..."
dotnet restore
if [ $? -ne 0 ]; then
    print_color $RED "Error: Package restore failed."
    exit 1
fi

# Build project
print_color $GREEN "Building project..."
dotnet build -c $CONFIGURATION --no-restore
if [ $? -ne 0 ]; then
    print_color $RED "Error: Build failed."
    exit 1
fi

# Prepare publish arguments
PUBLISH_ARGS="publish -c $CONFIGURATION -r $RUNTIME --no-build -o ../publish/$RUNTIME"

if [ "$SELF_CONTAINED" = true ]; then
    PUBLISH_ARGS="$PUBLISH_ARGS --self-contained"
else
    PUBLISH_ARGS="$PUBLISH_ARGS --no-self-contained"
fi

if [ "$SINGLE_FILE" = true ]; then
    PUBLISH_ARGS="$PUBLISH_ARGS -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true"
fi

# Publish project
print_color $GREEN "Publishing project..."
print_color $CYAN "Command: dotnet $PUBLISH_ARGS"
dotnet $PUBLISH_ARGS

if [ $? -ne 0 ]; then
    print_color $RED "Error: Publish failed."
    exit 1
fi

# Return to root directory
cd ..

# Copy additional files
print_color $GREEN "Copying additional files..."
PUBLISH_DIR="publish/$RUNTIME"

# Copy documentation files
cp README.md "$PUBLISH_DIR/"
cp LICENSE "$PUBLISH_DIR/"
cp CHANGELOG.md "$PUBLISH_DIR/"
cp USAGE.md "$PUBLISH_DIR/"
cp INSTALL.md "$PUBLISH_DIR/"
cp FAQ.md "$PUBLISH_DIR/"

# Create version info file
cat > "$PUBLISH_DIR/VERSION.txt" << EOF
System Cleaner Pro - منظف النظام الاحترافي
Version: 1.0.0
Build Date: $(date '+%Y-%m-%d %H:%M:%S')
Configuration: $CONFIGURATION
Runtime: $RUNTIME
Self-Contained: $SELF_CONTAINED
Single File: $SINGLE_FILE

For more information, visit:
https://github.com/your-repo/SystemCleanerPro

© 2024 System Cleaner Pro. Licensed under MIT License.
EOF

# Create run script for Windows
if [[ $RUNTIME == win-* ]]; then
    cat > "$PUBLISH_DIR/Run.bat" << 'EOF'
@echo off
echo Starting System Cleaner Pro...
echo Please run as Administrator for full functionality.
echo.
SystemCleanerPro.exe
pause
EOF
fi

# Create run script for Linux/Mac
if [[ $RUNTIME == linux-* ]] || [[ $RUNTIME == osx-* ]]; then
    cat > "$PUBLISH_DIR/run.sh" << 'EOF'
#!/bin/bash
echo "Starting System Cleaner Pro..."
echo "Please run with sudo for full functionality."
echo ""
./SystemCleanerPro
EOF
    chmod +x "$PUBLISH_DIR/run.sh"
fi

# Calculate total size
TOTAL_SIZE=$(du -sh "$PUBLISH_DIR" | cut -f1)
FILE_COUNT=$(find "$PUBLISH_DIR" -type f | wc -l)

print_color $GREEN "=== Build Completed Successfully ==="
print_color $YELLOW "Output Directory: $PUBLISH_DIR"
print_color $YELLOW "Total Size: $TOTAL_SIZE"
print_color $YELLOW "Files Count: $FILE_COUNT"

# Show main files
print_color $CYAN "\nMain Files:"
ls -lh "$PUBLISH_DIR" | grep -E '\.(exe|dll|txt|md)$' | awk '{print $9 "\t" $5}'

print_color $GREEN "Build completed! You can now run the application from: $PUBLISH_DIR/SystemCleanerPro"
if [[ $RUNTIME == win-* ]]; then
    print_color $YELLOW "Remember to run as Administrator for full functionality."
else
    print_color $YELLOW "Remember to run with sudo for full functionality."
fi

# Optional: Open output folder (Linux with GUI)
if command -v xdg-open &> /dev/null; then
    read -p "Open output folder? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        xdg-open "$PUBLISH_DIR"
    fi
fi
