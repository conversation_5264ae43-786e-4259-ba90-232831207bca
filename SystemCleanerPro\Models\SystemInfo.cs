using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace SystemCleanerPro.Models
{
    /// <summary>
    /// نموذج معلومات النظام
    /// </summary>
    public class SystemInfo : INotifyPropertyChanged
    {
        private double _cpuUsage;
        private double _memoryUsage;
        private long _totalMemory;
        private long _availableMemory;
        private int _systemScore;
        private string _osVersion = string.Empty;
        private string _computerName = string.Empty;

        public double CpuUsage
        {
            get => _cpuUsage;
            set
            {
                _cpuUsage = value;
                OnPropertyChanged();
            }
        }

        public double MemoryUsage
        {
            get => _memoryUsage;
            set
            {
                _memoryUsage = value;
                OnPropertyChanged();
            }
        }

        public long TotalMemory
        {
            get => _totalMemory;
            set
            {
                _totalMemory = value;
                OnPropertyChanged();
            }
        }

        public long AvailableMemory
        {
            get => _availableMemory;
            set
            {
                _availableMemory = value;
                OnPropertyChanged();
            }
        }

        public int SystemScore
        {
            get => _systemScore;
            set
            {
                _systemScore = value;
                OnPropertyChanged();
            }
        }

        public string OSVersion
        {
            get => _osVersion;
            set
            {
                _osVersion = value;
                OnPropertyChanged();
            }
        }

        public string ComputerName
        {
            get => _computerName;
            set
            {
                _computerName = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
