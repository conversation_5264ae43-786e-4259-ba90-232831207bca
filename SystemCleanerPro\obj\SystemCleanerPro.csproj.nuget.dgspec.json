{"format": 1, "restore": {"D:\\generationCleaning\\SystemCleanerPro\\SystemCleanerPro.csproj": {}}, "projects": {"D:\\generationCleaning\\SystemCleanerPro\\SystemCleanerPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\generationCleaning\\SystemCleanerPro\\SystemCleanerPro.csproj", "projectName": "SystemCleanerPro", "projectPath": "D:\\generationCleaning\\SystemCleanerPro\\SystemCleanerPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\generationCleaning\\SystemCleanerPro\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"LiveCharts.Wpf": {"target": "Package", "version": "[0.9.7, )"}, "MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.Win32.Registry": {"target": "Package", "version": "[5.0.0, )"}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}, "System.ServiceProcess.ServiceController": {"target": "Package", "version": "[8.0.0, )"}, "TaskScheduler": {"target": "Package", "version": "[2.10.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}