using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Win32;
using SystemCleanerPro.Models;

namespace SystemCleanerPro.Services
{
    /// <summary>
    /// خدمة إدارة برامج الإقلاع
    /// </summary>
    public class StartupManagerService
    {
        private readonly List<string> _registryStartupPaths;
        private readonly List<string> _startupFolderPaths;

        public StartupManagerService()
        {
            _registryStartupPaths = new List<string>
            {
                @"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
                @"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce",
                @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Run",
                @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\RunOnce"
            };

            _startupFolderPaths = new List<string>
            {
                Environment.GetFolderPath(Environment.SpecialFolder.Startup),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonStartup))
            };
        }

        public async Task<ObservableCollection<StartupItem>> GetStartupItemsAsync()
        {
            var startupItems = new ObservableCollection<StartupItem>();

            await Task.Run(() =>
            {
                // الحصول على عناصر السجل
                foreach (var registryPath in _registryStartupPaths)
                {
                    var items = GetRegistryStartupItems(registryPath);
                    foreach (var item in items)
                    {
                        startupItems.Add(item);
                    }
                }

                // الحصول على عناصر مجلد البدء
                foreach (var folderPath in _startupFolderPaths)
                {
                    var items = GetFolderStartupItems(folderPath);
                    foreach (var item in items)
                    {
                        startupItems.Add(item);
                    }
                }
            });

            return startupItems;
        }

        private List<StartupItem> GetRegistryStartupItems(string registryPath)
        {
            var items = new List<StartupItem>();

            try
            {
                using (var key = Registry.LocalMachine.OpenSubKey(registryPath))
                {
                    if (key != null)
                    {
                        foreach (string valueName in key.GetValueNames())
                        {
                            try
                            {
                                var value = key.GetValue(valueName) as string;
                                if (!string.IsNullOrEmpty(value))
                                {
                                    var item = new StartupItem
                                    {
                                        Name = valueName,
                                        Path = value,
                                        Location = registryPath,
                                        Type = StartupType.Registry,
                                        IsEnabled = true,
                                        Publisher = GetFilePublisher(ExtractExecutablePath(value))
                                    };
                                    items.Add(item);
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"خطأ في قراءة عنصر البدء {valueName}: {ex.Message}");
                            }
                        }
                    }
                }

                // فحص السجل للمستخدم الحالي أيضاً
                using (var key = Registry.CurrentUser.OpenSubKey(registryPath.Replace("SOFTWARE\\", "")))
                {
                    if (key != null)
                    {
                        foreach (string valueName in key.GetValueNames())
                        {
                            try
                            {
                                var value = key.GetValue(valueName) as string;
                                if (!string.IsNullOrEmpty(value))
                                {
                                    var item = new StartupItem
                                    {
                                        Name = valueName,
                                        Path = value,
                                        Location = "HKCU\\" + registryPath.Replace("SOFTWARE\\", ""),
                                        Type = StartupType.Registry,
                                        IsEnabled = true,
                                        Publisher = GetFilePublisher(ExtractExecutablePath(value))
                                    };
                                    items.Add(item);
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"خطأ في قراءة عنصر البدء {valueName}: {ex.Message}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في قراءة مسار السجل {registryPath}: {ex.Message}");
            }

            return items;
        }

        private List<StartupItem> GetFolderStartupItems(string folderPath)
        {
            var items = new List<StartupItem>();

            try
            {
                if (Directory.Exists(folderPath))
                {
                    var files = Directory.GetFiles(folderPath, "*.*", SearchOption.TopDirectoryOnly);
                    
                    foreach (var file in files)
                    {
                        try
                        {
                            var fileInfo = new FileInfo(file);
                            var item = new StartupItem
                            {
                                Name = Path.GetFileNameWithoutExtension(file),
                                Path = file,
                                Location = folderPath,
                                Type = StartupType.StartupFolder,
                                IsEnabled = true,
                                Publisher = GetFilePublisher(file)
                            };
                            items.Add(item);
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"خطأ في قراءة ملف البدء {file}: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في قراءة مجلد البدء {folderPath}: {ex.Message}");
            }

            return items;
        }

        public async Task<bool> DisableStartupItemAsync(StartupItem item)
        {
            return await Task.Run(() =>
            {
                try
                {
                    switch (item.Type)
                    {
                        case StartupType.Registry:
                            return DisableRegistryStartupItem(item);
                        case StartupType.StartupFolder:
                            return DisableStartupFolderItem(item);
                        default:
                            return false;
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"خطأ في تعطيل عنصر البدء {item.Name}: {ex.Message}");
                    return false;
                }
            });
        }

        public async Task<bool> EnableStartupItemAsync(StartupItem item)
        {
            return await Task.Run(() =>
            {
                try
                {
                    switch (item.Type)
                    {
                        case StartupType.Registry:
                            return EnableRegistryStartupItem(item);
                        case StartupType.StartupFolder:
                            return EnableStartupFolderItem(item);
                        default:
                            return false;
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"خطأ في تفعيل عنصر البدء {item.Name}: {ex.Message}");
                    return false;
                }
            });
        }

        private bool DisableRegistryStartupItem(StartupItem item)
        {
            try
            {
                var isCurrentUser = item.Location.StartsWith("HKCU");
                var registryPath = isCurrentUser 
                    ? item.Location.Replace("HKCU\\", "") 
                    : item.Location;

                var rootKey = isCurrentUser ? Registry.CurrentUser : Registry.LocalMachine;

                using (var key = rootKey.OpenSubKey(registryPath, true))
                {
                    if (key != null && key.GetValue(item.Name) != null)
                    {
                        // نسخ القيمة إلى مكان آمن للاستعادة لاحقاً
                        var backupPath = registryPath + "_Disabled";
                        using (var backupKey = rootKey.CreateSubKey(backupPath))
                        {
                            backupKey.SetValue(item.Name, item.Path);
                        }

                        // حذف القيمة الأصلية
                        key.DeleteValue(item.Name);
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تعطيل عنصر السجل {item.Name}: {ex.Message}");
            }

            return false;
        }

        private bool EnableRegistryStartupItem(StartupItem item)
        {
            try
            {
                var isCurrentUser = item.Location.StartsWith("HKCU");
                var registryPath = isCurrentUser 
                    ? item.Location.Replace("HKCU\\", "") 
                    : item.Location;

                var rootKey = isCurrentUser ? Registry.CurrentUser : Registry.LocalMachine;

                // البحث عن القيمة في مكان النسخ الاحتياطي
                var backupPath = registryPath + "_Disabled";
                using (var backupKey = rootKey.OpenSubKey(backupPath))
                {
                    if (backupKey != null)
                    {
                        var value = backupKey.GetValue(item.Name) as string;
                        if (!string.IsNullOrEmpty(value))
                        {
                            // استعادة القيمة
                            using (var key = rootKey.CreateSubKey(registryPath))
                            {
                                key.SetValue(item.Name, value);
                            }

                            // حذف النسخة الاحتياطية
                            using (var backupKeyWrite = rootKey.OpenSubKey(backupPath, true))
                            {
                                backupKeyWrite?.DeleteValue(item.Name);
                            }

                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تفعيل عنصر السجل {item.Name}: {ex.Message}");
            }

            return false;
        }

        private bool DisableStartupFolderItem(StartupItem item)
        {
            try
            {
                if (File.Exists(item.Path))
                {
                    var disabledPath = item.Path + ".disabled";
                    File.Move(item.Path, disabledPath);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تعطيل ملف البدء {item.Name}: {ex.Message}");
            }

            return false;
        }

        private bool EnableStartupFolderItem(StartupItem item)
        {
            try
            {
                var disabledPath = item.Path + ".disabled";
                if (File.Exists(disabledPath))
                {
                    File.Move(disabledPath, item.Path);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تفعيل ملف البدء {item.Name}: {ex.Message}");
            }

            return false;
        }

        private string ExtractExecutablePath(string commandLine)
        {
            if (string.IsNullOrEmpty(commandLine))
                return string.Empty;

            // إزالة المعاملات والحصول على مسار الملف التنفيذي فقط
            var path = commandLine.Trim();
            
            if (path.StartsWith("\""))
            {
                var endQuote = path.IndexOf("\"", 1);
                if (endQuote > 0)
                    path = path.Substring(1, endQuote - 1);
            }
            else
            {
                var spaceIndex = path.IndexOf(" ");
                if (spaceIndex > 0)
                    path = path.Substring(0, spaceIndex);
            }

            return path;
        }

        private string GetFilePublisher(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    var versionInfo = FileVersionInfo.GetVersionInfo(filePath);
                    return versionInfo.CompanyName ?? "غير معروف";
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }

            return "غير معروف";
        }
    }
}
