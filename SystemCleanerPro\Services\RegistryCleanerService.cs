using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Win32;
using SystemCleanerPro.Models;

namespace SystemCleanerPro.Services
{
    /// <summary>
    /// خدمة تنظيف السجل
    /// </summary>
    public class RegistryCleanerService
    {
        private readonly List<string> _registryPaths;
        
        public event EventHandler<string>? ProgressUpdated;

        public RegistryCleanerService()
        {
            _registryPaths = new List<string>
            {
                @"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
                @"SOFTWARE\Classes\CLSID",
                @"SOFTWARE\Classes\Interface",
                @"SOFTWARE\Classes\TypeLib",
                @"SOFTWARE\Microsoft\Windows\CurrentVersion\SharedDLLs",
                @"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths"
            };
        }

        public async Task<CleaningResult> CleanRegistryAsync()
        {
            var result = new CleaningResult
            {
                CleaningType = "تنظيف السجل",
                CleaningDate = DateTime.Now
            };

            var startTime = DateTime.Now;
            long totalKeysDeleted = 0;

            try
            {
                // إنشاء نقطة استعادة قبل التنظيف
                ProgressUpdated?.Invoke(this, "إنشاء نقطة استعادة...");
                await CreateSystemRestorePointAsync();

                // تنظيف مفاتيح السجل
                foreach (var path in _registryPaths)
                {
                    ProgressUpdated?.Invoke(this, $"تنظيف: {path}");
                    var keysDeleted = await CleanRegistryPathAsync(path);
                    totalKeysDeleted += keysDeleted;
                }

                result.TotalFilesDeleted = totalKeysDeleted;
                result.CleaningDuration = DateTime.Now - startTime;
                result.IsSuccessful = true;
                result.CleanedLocations.AddRange(_registryPaths);
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في تنظيف السجل: {ex.Message}");
                result.IsSuccessful = false;
            }

            return result;
        }

        private async Task<long> CleanRegistryPathAsync(string registryPath)
        {
            long keysDeleted = 0;

            await Task.Run(() =>
            {
                try
                {
                    using (var key = Registry.LocalMachine.OpenSubKey(registryPath, true))
                    {
                        if (key != null)
                        {
                            keysDeleted += CleanInvalidKeys(key);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"خطأ في تنظيف مسار السجل {registryPath}: {ex.Message}");
                }
            });

            return keysDeleted;
        }

        private long CleanInvalidKeys(RegistryKey parentKey)
        {
            long keysDeleted = 0;
            var keysToDelete = new List<string>();

            try
            {
                foreach (string subKeyName in parentKey.GetSubKeyNames())
                {
                    try
                    {
                        using (var subKey = parentKey.OpenSubKey(subKeyName))
                        {
                            if (subKey != null && IsInvalidKey(subKey))
                            {
                                keysToDelete.Add(subKeyName);
                            }
                        }
                    }
                    catch
                    {
                        // إذا لم نتمكن من فتح المفتاح، فقد يكون تالفاً
                        keysToDelete.Add(subKeyName);
                    }
                }

                // حذف المفاتيح التالفة
                foreach (string keyName in keysToDelete)
                {
                    try
                    {
                        parentKey.DeleteSubKeyTree(keyName);
                        keysDeleted++;
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"لا يمكن حذف المفتاح {keyName}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تنظيف المفاتيح: {ex.Message}");
            }

            return keysDeleted;
        }

        private bool IsInvalidKey(RegistryKey key)
        {
            try
            {
                // فحص مفاتيح البرامج المحذوفة
                if (key.Name.Contains("Uninstall"))
                {
                    var displayName = key.GetValue("DisplayName") as string;
                    var installLocation = key.GetValue("InstallLocation") as string;
                    
                    if (!string.IsNullOrEmpty(installLocation) && !Directory.Exists(installLocation))
                    {
                        return true;
                    }
                }

                // فحص مفاتيح CLSID
                if (key.Name.Contains("CLSID"))
                {
                    var inprocServer = key.GetValue("InprocServer32") as string;
                    if (!string.IsNullOrEmpty(inprocServer) && !File.Exists(inprocServer))
                    {
                        return true;
                    }
                }

                // فحص مفاتيح SharedDLLs
                if (key.Name.Contains("SharedDLLs"))
                {
                    foreach (string valueName in key.GetValueNames())
                    {
                        if (!File.Exists(valueName))
                        {
                            return true;
                        }
                    }
                }

                return false;
            }
            catch
            {
                // إذا حدث خطأ في القراءة، فالمفتاح قد يكون تالفاً
                return true;
            }
        }

        private async Task CreateSystemRestorePointAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var processInfo = new ProcessStartInfo
                    {
                        FileName = "powershell.exe",
                        Arguments = "-Command \"Checkpoint-Computer -Description 'System Cleaner Pro - Before Registry Clean' -RestorePointType 'MODIFY_SETTINGS'\"",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    };

                    using (var process = Process.Start(processInfo))
                    {
                        process?.WaitForExit(30000); // انتظار 30 ثانية كحد أقصى
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"فشل في إنشاء نقطة الاستعادة: {ex.Message}");
                    // لا نرمي استثناء هنا لأن تنظيف السجل يمكن أن يستمر
                }
            });
        }

        public async Task<bool> IsRegistryCleaningNeededAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    int invalidKeysCount = 0;
                    
                    foreach (var path in _registryPaths)
                    {
                        using (var key = Registry.LocalMachine.OpenSubKey(path))
                        {
                            if (key != null)
                            {
                                invalidKeysCount += CountInvalidKeys(key);
                                if (invalidKeysCount > 10) // إذا وجدنا أكثر من 10 مفاتيح تالفة
                                    return true;
                            }
                        }
                    }

                    return invalidKeysCount > 0;
                }
                catch
                {
                    return false;
                }
            });
        }

        private int CountInvalidKeys(RegistryKey parentKey)
        {
            int count = 0;
            
            try
            {
                foreach (string subKeyName in parentKey.GetSubKeyNames())
                {
                    try
                    {
                        using (var subKey = parentKey.OpenSubKey(subKeyName))
                        {
                            if (subKey != null && IsInvalidKey(subKey))
                            {
                                count++;
                            }
                        }
                    }
                    catch
                    {
                        count++;
                    }
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }

            return count;
        }
    }
}
