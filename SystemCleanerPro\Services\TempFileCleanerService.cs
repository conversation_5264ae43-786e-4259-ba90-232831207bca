using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using SystemCleanerPro.Models;

namespace SystemCleanerPro.Services
{
    /// <summary>
    /// خدمة تنظيف الملفات المؤقتة
    /// </summary>
    public class TempFileCleanerService
    {
        private readonly List<string> _tempLocations;
        private readonly List<string> _logExtensions;

        public event EventHandler<string>? ProgressUpdated;

        public TempFileCleanerService()
        {
            _tempLocations = new List<string>
            {
                Environment.GetFolderPath(Environment.SpecialFolder.InternetCache),
                Path.GetTempPath(),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Temp"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Prefetch"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Recent)),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Temp")
            };

            _logExtensions = new List<string> { ".log", ".tmp", ".temp", ".bak", ".old", ".chk", ".dmp" };
        }

        public async Task<CleaningResult> CleanTempFilesAsync()
        {
            var result = new CleaningResult
            {
                CleaningType = "تنظيف الملفات المؤقتة",
                CleaningDate = DateTime.Now
            };

            var startTime = DateTime.Now;
            long totalFilesDeleted = 0;
            long totalSpaceFreed = 0;

            try
            {
                foreach (var location in _tempLocations)
                {
                    if (Directory.Exists(location))
                    {
                        ProgressUpdated?.Invoke(this, $"تنظيف: {location}");
                        
                        var locationResult = await CleanDirectoryAsync(location);
                        totalFilesDeleted += locationResult.FilesDeleted;
                        totalSpaceFreed += locationResult.SpaceFreed;
                        
                        result.CleanedLocations.Add(location);
                    }
                }

                // تنظيف ملفات السجل من جميع الأقراص
                var logResult = await CleanLogFilesAsync(result);
                totalFilesDeleted += logResult.FilesDeleted;
                totalSpaceFreed += logResult.SpaceFreed;

                result.TotalFilesDeleted = totalFilesDeleted;
                result.TotalSpaceFreed = totalSpaceFreed;
                result.CleaningDuration = DateTime.Now - startTime;
                result.IsSuccessful = true;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ عام: {ex.Message}");
                result.IsSuccessful = false;
            }

            return result;
        }

        private async Task<(long FilesDeleted, long SpaceFreed)> CleanDirectoryAsync(string directoryPath)
        {
            long filesDeleted = 0;
            long spaceFreed = 0;

            try
            {
                var files = Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories);
                
                foreach (var file in files)
                {
                    try
                    {
                        var fileInfo = new FileInfo(file);
                        
                        // تحقق من أن الملف قديم (أكثر من يوم واحد)
                        if (DateTime.Now - fileInfo.LastAccessTime > TimeSpan.FromDays(1))
                        {
                            spaceFreed += fileInfo.Length;
                            File.Delete(file);
                            filesDeleted++;
                        }
                    }
                    catch (Exception ex)
                    {
                        // تجاهل الأخطاء الفردية للملفات المحمية
                        System.Diagnostics.Debug.WriteLine($"لا يمكن حذف الملف {file}: {ex.Message}");
                    }
                }

                // حذف المجلدات الفارغة
                await DeleteEmptyDirectoriesAsync(directoryPath);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف المجلد {directoryPath}: {ex.Message}");
            }

            return (filesDeleted, spaceFreed);
        }

        private async Task<(long FilesDeleted, long SpaceFreed)> CleanLogFilesAsync(CleaningResult result)
        {
            long totalFilesDeleted = 0;
            long totalSpaceFreed = 0;

            try
            {
                var drives = DriveInfo.GetDrives().Where(d => d.IsReady && d.DriveType == DriveType.Fixed);

                foreach (var drive in drives)
                {
                    ProgressUpdated?.Invoke(this, $"البحث عن ملفات السجل في {drive.Name}");

                    await Task.Run(() =>
                    {
                        SearchAndDeleteLogFiles(drive.RootDirectory, ref totalFilesDeleted, ref totalSpaceFreed);
                    });
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في تنظيف ملفات السجل: {ex.Message}");
            }

            return (totalFilesDeleted, totalSpaceFreed);
        }

        private void SearchAndDeleteLogFiles(DirectoryInfo directory, ref long filesDeleted, ref long spaceFreed)
        {
            try
            {
                foreach (var file in directory.GetFiles())
                {
                    if (_logExtensions.Contains(file.Extension.ToLower()) && 
                        DateTime.Now - file.LastWriteTime > TimeSpan.FromDays(7))
                    {
                        try
                        {
                            spaceFreed += file.Length;
                            file.Delete();
                            filesDeleted++;
                        }
                        catch
                        {
                            // تجاهل الأخطاء
                        }
                    }
                }

                foreach (var subDir in directory.GetDirectories())
                {
                    if (!IsSystemDirectory(subDir.Name))
                    {
                        SearchAndDeleteLogFiles(subDir, ref filesDeleted, ref spaceFreed);
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء الوصول
            }
        }

        private bool IsSystemDirectory(string dirName)
        {
            var systemDirs = new[] { "System32", "SysWOW64", "Windows", "Program Files", "Program Files (x86)" };
            return systemDirs.Any(sysDir => dirName.Equals(sysDir, StringComparison.OrdinalIgnoreCase));
        }

        private async Task DeleteEmptyDirectoriesAsync(string directoryPath)
        {
            try
            {
                await Task.Run(() =>
                {
                    var directories = Directory.GetDirectories(directoryPath, "*", SearchOption.AllDirectories)
                                               .OrderByDescending(d => d.Length);

                    foreach (var dir in directories)
                    {
                        try
                        {
                            if (!Directory.EnumerateFileSystemEntries(dir).Any())
                            {
                                Directory.Delete(dir);
                            }
                        }
                        catch
                        {
                            // تجاهل الأخطاء
                        }
                    }
                });
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }
    }
}
