using System;
using System.Windows;
using SystemCleanerPro.ViewModels;

namespace SystemCleanerPro.Views
{
    /// <summary>
    /// نافذة الإعدادات
    /// </summary>
    public partial class SettingsWindow : Window
    {
        private SettingsViewModel _viewModel;

        public SettingsWindow()
        {
            InitializeComponent();
            
            _viewModel = new SettingsViewModel();
            DataContext = _viewModel;
            
            _viewModel.SettingsSaved += OnSettingsSaved;
            _viewModel.SettingsCancelled += OnSettingsCancelled;
        }

        private void OnSettingsSaved(object? sender, EventArgs e)
        {
            DialogResult = true;
            Close();
        }

        private void OnSettingsCancelled(object? sender, EventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            if (_viewModel != null)
            {
                _viewModel.SettingsSaved -= OnSettingsSaved;
                _viewModel.SettingsCancelled -= OnSettingsCancelled;
            }
            base.OnClosed(e);
        }
    }
}
