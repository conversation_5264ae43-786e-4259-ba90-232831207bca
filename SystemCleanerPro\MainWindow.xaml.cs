﻿using System;
using System.Windows;
using SystemCleanerPro.ViewModels;

namespace SystemCleanerPro;

/// <summary>
/// النافذة الرئيسية للتطبيق
/// </summary>
public partial class MainWindow : Window
{
    private MainViewModel? _viewModel;

    public MainWindow()
    {
        InitializeComponent();
        InitializeWindow();
    }

    private void InitializeWindow()
    {
        // تعيين ViewModel
        _viewModel = DataContext as MainViewModel;

        // تعيين خصائص النافذة
        WindowState = WindowState.Normal;

        // التأكد من أن النافذة تظهر في المقدمة
        Topmost = false;
        ShowInTaskbar = true;
    }

    protected override void OnClosed(EventArgs e)
    {
        // تنظيف الموارد
        _viewModel?.Dispose();
        base.OnClosed(e);
    }

    private void Window_StateChanged(object sender, EventArgs e)
    {
        // التعامل مع تغيير حالة النافذة
        if (WindowState == WindowState.Minimized)
        {
            // يمكن إضافة منطق إخفاء النافذة في النظام tray هنا
        }
    }
}