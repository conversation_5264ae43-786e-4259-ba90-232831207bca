using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using SystemCleanerPro.Models;
using SystemCleanerPro.Services;
using SystemCleanerPro.Utilities;

namespace SystemCleanerPro.ViewModels
{
    /// <summary>
    /// ViewModel لنافذة إدارة برامج الإقلاع
    /// </summary>
    public class StartupManagerViewModel : INotifyPropertyChanged
    {
        private readonly StartupManagerService _startupManagerService;
        private ObservableCollection<StartupItem> _startupItems;
        private StartupItem? _selectedStartupItem;
        private string _statusMessage = "جاهز";
        private bool _isLoading;

        public StartupManagerViewModel()
        {
            _startupManagerService = new StartupManagerService();
            _startupItems = new ObservableCollection<StartupItem>();
            
            InitializeCommands();
            LoadStartupItemsAsync();
        }

        #region Properties

        public ObservableCollection<StartupItem> StartupItems
        {
            get => _startupItems;
            set
            {
                _startupItems = value;
                OnPropertyChanged();
            }
        }

        public StartupItem? SelectedStartupItem
        {
            get => _selectedStartupItem;
            set
            {
                _selectedStartupItem = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HasSelectedItem));
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public bool HasSelectedItem => SelectedStartupItem != null;

        #endregion

        #region Commands

        public ICommand RefreshCommand { get; private set; } = null!;
        public ICommand ToggleStartupItemCommand { get; private set; } = null!;
        public ICommand EnableSelectedCommand { get; private set; } = null!;
        public ICommand DisableSelectedCommand { get; private set; } = null!;
        public ICommand CloseCommand { get; private set; } = null!;

        #endregion

        private void InitializeCommands()
        {
            RefreshCommand = new RelayCommand(async () => await LoadStartupItemsAsync(), () => !IsLoading);
            ToggleStartupItemCommand = new RelayCommand<StartupItem>(async item => await ToggleStartupItemAsync(item));
            EnableSelectedCommand = new RelayCommand(async () => await EnableSelectedItemAsync(), () => HasSelectedItem && !IsLoading);
            DisableSelectedCommand = new RelayCommand(async () => await DisableSelectedItemAsync(), () => HasSelectedItem && !IsLoading);
            CloseCommand = new RelayCommand(CloseWindow);
        }

        private async Task LoadStartupItemsAsync()
        {
            IsLoading = true;
            StatusMessage = "جاري تحميل قائمة برامج الإقلاع...";

            try
            {
                var items = await _startupManagerService.GetStartupItemsAsync();
                StartupItems.Clear();
                
                foreach (var item in items.OrderBy(x => x.Name))
                {
                    StartupItems.Add(item);
                }

                StatusMessage = $"تم تحميل {StartupItems.Count} عنصر من برامج الإقلاع";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحميل برامج الإقلاع: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task ToggleStartupItemAsync(StartupItem? item)
        {
            if (item == null) return;

            try
            {
                StatusMessage = $"جاري تغيير حالة {item.Name}...";

                bool success;
                if (item.IsEnabled)
                {
                    success = await _startupManagerService.DisableStartupItemAsync(item);
                    if (success)
                    {
                        item.IsEnabled = false;
                        StatusMessage = $"تم تعطيل {item.Name}";
                    }
                    else
                    {
                        StatusMessage = $"فشل في تعطيل {item.Name}";
                    }
                }
                else
                {
                    success = await _startupManagerService.EnableStartupItemAsync(item);
                    if (success)
                    {
                        item.IsEnabled = true;
                        StatusMessage = $"تم تفعيل {item.Name}";
                    }
                    else
                    {
                        StatusMessage = $"فشل في تفعيل {item.Name}";
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تغيير حالة {item.Name}: {ex.Message}";
            }
        }

        private async Task EnableSelectedItemAsync()
        {
            if (SelectedStartupItem == null) return;

            try
            {
                StatusMessage = $"جاري تفعيل {SelectedStartupItem.Name}...";
                
                var success = await _startupManagerService.EnableStartupItemAsync(SelectedStartupItem);
                if (success)
                {
                    SelectedStartupItem.IsEnabled = true;
                    StatusMessage = $"تم تفعيل {SelectedStartupItem.Name}";
                }
                else
                {
                    StatusMessage = $"فشل في تفعيل {SelectedStartupItem.Name}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تفعيل {SelectedStartupItem.Name}: {ex.Message}";
            }
        }

        private async Task DisableSelectedItemAsync()
        {
            if (SelectedStartupItem == null) return;

            try
            {
                StatusMessage = $"جاري تعطيل {SelectedStartupItem.Name}...";
                
                var success = await _startupManagerService.DisableStartupItemAsync(SelectedStartupItem);
                if (success)
                {
                    SelectedStartupItem.IsEnabled = false;
                    StatusMessage = $"تم تعطيل {SelectedStartupItem.Name}";
                }
                else
                {
                    StatusMessage = $"فشل في تعطيل {SelectedStartupItem.Name}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تعطيل {SelectedStartupItem.Name}: {ex.Message}";
            }
        }

        private void CloseWindow()
        {
            // سيتم تنفيذ هذا في code-behind
            CloseRequested?.Invoke(this, EventArgs.Empty);
        }

        public event EventHandler? CloseRequested;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
