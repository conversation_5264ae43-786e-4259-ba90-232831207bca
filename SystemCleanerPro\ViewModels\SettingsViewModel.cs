using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using SystemCleanerPro.Services;
using SystemCleanerPro.Utilities;

namespace SystemCleanerPro.ViewModels
{
    /// <summary>
    /// ViewModel لنافذة الإعدادات
    /// </summary>
    public class SettingsViewModel : INotifyPropertyChanged
    {
        private bool _startWithWindows;
        private bool _minimizeToTray;
        private bool _showNotifications;
        private string _selectedLanguage = "ar";
        private string _selectedTheme = "Light";
        private double _cpuAlertThreshold = 80;
        private double _memoryAlertThreshold = 85;
        private ScheduleSettings _scheduleSettings;

        public SettingsViewModel()
        {
            _scheduleSettings = new ScheduleSettings();
            InitializeCommands();
            LoadSettings();
        }

        #region Properties

        public bool StartWithWindows
        {
            get => _startWithWindows;
            set
            {
                _startWithWindows = value;
                OnPropertyChanged();
            }
        }

        public bool MinimizeToTray
        {
            get => _minimizeToTray;
            set
            {
                _minimizeToTray = value;
                OnPropertyChanged();
            }
        }

        public bool ShowNotifications
        {
            get => _showNotifications;
            set
            {
                _showNotifications = value;
                OnPropertyChanged();
            }
        }

        public string SelectedLanguage
        {
            get => _selectedLanguage;
            set
            {
                _selectedLanguage = value;
                OnPropertyChanged();
            }
        }

        public string SelectedTheme
        {
            get => _selectedTheme;
            set
            {
                _selectedTheme = value;
                OnPropertyChanged();
            }
        }

        public double CpuAlertThreshold
        {
            get => _cpuAlertThreshold;
            set
            {
                _cpuAlertThreshold = value;
                OnPropertyChanged();
            }
        }

        public double MemoryAlertThreshold
        {
            get => _memoryAlertThreshold;
            set
            {
                _memoryAlertThreshold = value;
                OnPropertyChanged();
            }
        }

        public ScheduleSettings ScheduleSettings
        {
            get => _scheduleSettings;
            set
            {
                _scheduleSettings = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsCustomFrequency));
            }
        }

        public bool IsCustomFrequency => ScheduleSettings.Frequency == CleaningFrequency.Custom;

        public List<LanguageOption> AvailableLanguages { get; } = new List<LanguageOption>
        {
            new LanguageOption { Code = "ar", Name = "العربية" },
            new LanguageOption { Code = "en", Name = "English" }
        };

        public List<string> AvailableThemes { get; } = new List<string>
        {
            "Light", "Dark"
        };

        #endregion

        #region Commands

        public ICommand SaveCommand { get; private set; } = null!;
        public ICommand CancelCommand { get; private set; } = null!;
        public ICommand RestoreDefaultsCommand { get; private set; } = null!;

        #endregion

        private void InitializeCommands()
        {
            SaveCommand = new RelayCommand(SaveSettings);
            CancelCommand = new RelayCommand(CancelSettings);
            RestoreDefaultsCommand = new RelayCommand(RestoreDefaults);
        }

        private void LoadSettings()
        {
            try
            {
                // تحميل الإعدادات من التخزين المحلي أو الافتراضية
                // هنا يمكن إضافة منطق تحميل الإعدادات من ملف أو السجل
                
                StartWithWindows = false;
                MinimizeToTray = true;
                ShowNotifications = true;
                SelectedLanguage = "ar";
                SelectedTheme = "Light";
                CpuAlertThreshold = 80;
                MemoryAlertThreshold = 85;

                ScheduleSettings = new ScheduleSettings
                {
                    IsEnabled = false,
                    Frequency = CleaningFrequency.Daily,
                    CustomInterval = 60,
                    CleanTempFiles = true,
                    OptimizeMemory = true,
                    CleanRegistry = false,
                    SilentMode = true
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإعدادات: {ex.Message}");
            }
        }

        private void SaveSettings()
        {
            try
            {
                // حفظ الإعدادات
                // هنا يمكن إضافة منطق حفظ الإعدادات في ملف أو السجل
                
                SettingsSaved?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الإعدادات: {ex.Message}");
            }
        }

        private void CancelSettings()
        {
            // إلغاء التغييرات واستعادة الإعدادات الأصلية
            LoadSettings();
            SettingsCancelled?.Invoke(this, EventArgs.Empty);
        }

        private void RestoreDefaults()
        {
            StartWithWindows = false;
            MinimizeToTray = true;
            ShowNotifications = true;
            SelectedLanguage = "ar";
            SelectedTheme = "Light";
            CpuAlertThreshold = 80;
            MemoryAlertThreshold = 85;

            ScheduleSettings = new ScheduleSettings
            {
                IsEnabled = false,
                Frequency = CleaningFrequency.Daily,
                CustomInterval = 60,
                CleanTempFiles = true,
                OptimizeMemory = true,
                CleanRegistry = false,
                SilentMode = true
            };
        }

        public event EventHandler? SettingsSaved;
        public event EventHandler? SettingsCancelled;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// خيار اللغة
    /// </summary>
    public class LanguageOption
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }
}
