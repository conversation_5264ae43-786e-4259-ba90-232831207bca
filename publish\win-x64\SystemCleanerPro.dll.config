<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <!-- إعدادات التطبيق العامة -->
    <add key="ApplicationName" value="System Cleaner Pro" />
    <add key="Version" value="1.0.0" />
    <add key="DefaultLanguage" value="ar" />
    <add key="DefaultTheme" value="Light" />
    
    <!-- إعدادات التنظيف -->
    <add key="DefaultCleanTempFiles" value="true" />
    <add key="DefaultOptimizeMemory" value="true" />
    <add key="DefaultCleanRegistry" value="false" />
    <add key="CreateRestorePoint" value="true" />
    
    <!-- إعدادات المراقبة -->
    <add key="MonitoringInterval" value="1000" />
    <add key="CpuAlertThreshold" value="80" />
    <add key="MemoryAlertThreshold" value="85" />
    
    <!-- إعدادات الجدولة -->
    <add key="DefaultScheduleEnabled" value="false" />
    <add key="DefaultScheduleFrequency" value="Daily" />
    <add key="DefaultSilentMode" value="true" />
    
    <!-- إعدادات الأمان -->
    <add key="SkipSystemFiles" value="true" />
    <add key="SkipSystemDirectories" value="true" />
    <add key="MinFileAge" value="1" />
    
    <!-- إعدادات الواجهة -->
    <add key="StartWithWindows" value="false" />
    <add key="MinimizeToTray" value="true" />
    <add key="ShowNotifications" value="true" />
    <add key="AutoCheckUpdates" value="true" />
  </appSettings>
  
  <system.diagnostics>
    <trace autoflush="true">
      <listeners>
        <add name="fileListener" 
             type="System.Diagnostics.TextWriterTraceListener" 
             initializeData="SystemCleanerPro.log" />
      </listeners>
    </trace>
  </system.diagnostics>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" 
                          publicKeyToken="b03f5f7f11d50a3a" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
