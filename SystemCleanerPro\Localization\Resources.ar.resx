<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  
  <!-- النصوص العربية -->
  <data name="AppTitle" xml:space="preserve">
    <value>منظف النظام الاحترافي</value>
  </data>
  <data name="QuickClean" xml:space="preserve">
    <value>تنظيف سريع</value>
  </data>
  <data name="CleanTempFiles" xml:space="preserve">
    <value>تنظيف الملفات المؤقتة</value>
  </data>
  <data name="OptimizeMemory" xml:space="preserve">
    <value>تحسين الذاكرة</value>
  </data>
  <data name="CleanRegistry" xml:space="preserve">
    <value>تنظيف السجل</value>
  </data>
  <data name="StartupManager" xml:space="preserve">
    <value>إدارة برامج الإقلاع</value>
  </data>
  <data name="FindDuplicates" xml:space="preserve">
    <value>البحث عن المكررات</value>
  </data>
  <data name="CleanBrowsers" xml:space="preserve">
    <value>تنظيف المتصفحات</value>
  </data>
  <data name="DiskAnalyzer" xml:space="preserve">
    <value>تحليل القرص</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>الإعدادات</value>
  </data>
  <data name="CPU" xml:space="preserve">
    <value>المعالج</value>
  </data>
  <data name="Memory" xml:space="preserve">
    <value>الذاكرة</value>
  </data>
  <data name="SystemScore" xml:space="preserve">
    <value>نقاط النظام</value>
  </data>
  <data name="SystemStatus" xml:space="preserve">
    <value>حالة النظام</value>
  </data>
  <data name="CleaningReports" xml:space="preserve">
    <value>تقارير التنظيف</value>
  </data>
  <data name="Ready" xml:space="preserve">
    <value>جاهز</value>
  </data>
  <data name="Processing" xml:space="preserve">
    <value>جاري المعالجة...</value>
  </data>
  <data name="Completed" xml:space="preserve">
    <value>تم بنجاح</value>
  </data>
  <data name="Failed" xml:space="preserve">
    <value>فشل</value>
  </data>
  <data name="FilesDeleted" xml:space="preserve">
    <value>الملفات المحذوفة</value>
  </data>
  <data name="SpaceFreed" xml:space="preserve">
    <value>المساحة المحررة</value>
  </data>
  <data name="GeneralSettings" xml:space="preserve">
    <value>الإعدادات العامة</value>
  </data>
  <data name="AutoScheduling" xml:space="preserve">
    <value>التنظيف التلقائي</value>
  </data>
  <data name="Alerts" xml:space="preserve">
    <value>التنبيهات والمراقبة</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>اللغة</value>
  </data>
  <data name="Theme" xml:space="preserve">
    <value>الثيم</value>
  </data>
  <data name="StartWithWindows" xml:space="preserve">
    <value>تشغيل مع بدء النظام</value>
  </data>
  <data name="MinimizeToTray" xml:space="preserve">
    <value>تصغير إلى شريط المهام</value>
  </data>
  <data name="ShowNotifications" xml:space="preserve">
    <value>إظهار الإشعارات</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>إلغاء</value>
  </data>
  <data name="RestoreDefaults" xml:space="preserve">
    <value>استعادة الافتراضي</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>إغلاق</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>تفعيل</value>
  </data>
  <data name="Disable" xml:space="preserve">
    <value>تعطيل</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>الحالة</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="Publisher" xml:space="preserve">
    <value>الناشر</value>
  </data>
  <data name="Path" xml:space="preserve">
    <value>المسار</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>الموقع</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>النوع</value>
  </data>
  <data name="RefreshList" xml:space="preserve">
    <value>تحديث القائمة</value>
  </data>
  <data name="EnableSelected" xml:space="preserve">
    <value>تفعيل المحدد</value>
  </data>
  <data name="DisableSelected" xml:space="preserve">
    <value>تعطيل المحدد</value>
  </data>
</root>
