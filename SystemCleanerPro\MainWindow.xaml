﻿<Window x:Class="SystemCleanerPro.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SystemCleanerPro"
        mc:Ignorable="d"
        Title="System Cleaner Pro - منظف النظام الاحترافي"
        Height="700" Width="1200"
        MinHeight="600" MinWidth="900"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <!-- تعريف الألوان والأنماط -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="AccentBrush" Color="#FF4081"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20">
            <TextBlock Text="System Cleaner Pro - منظف النظام الاحترافي"
                      FontSize="20"
                      FontWeight="Bold"
                      Foreground="White"
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- المحتوى الرئيسي -->
        <StackPanel Grid.Row="1" Margin="20">
            <TextBlock Text="مرحباً بك في منظف النظام الاحترافي"
                      FontSize="24"
                      FontWeight="Bold"
                      HorizontalAlignment="Center"
                      Margin="0,20"/>

            <TextBlock Text="برنامج شامل لتنظيف وتحسين أداء الحاسوب"
                      FontSize="16"
                      HorizontalAlignment="Center"
                      Margin="0,10"/>

            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,30">
                <Button Content="تنظيف سريع"
                       Background="{StaticResource SuccessBrush}"
                       Foreground="White"
                       Padding="20,10"
                       Margin="10"
                       FontSize="16"/>

                <Button Content="تنظيف الملفات المؤقتة"
                       Background="{StaticResource PrimaryBrush}"
                       Foreground="White"
                       Padding="20,10"
                       Margin="10"
                       FontSize="16"/>

                <Button Content="تحسين الذاكرة"
                       Background="{StaticResource AccentBrush}"
                       Foreground="White"
                       Padding="20,10"
                       Margin="10"
                       FontSize="16"/>
            </StackPanel>

            <TextBlock Text="جاهز للاستخدام"
                      FontSize="14"
                      HorizontalAlignment="Center"
                      Margin="0,20"
                      Foreground="Gray"/>
        </StackPanel>

        <!-- شريط الحالة -->
        <Border Grid.Row="2" Background="LightGray" Padding="10">
            <TextBlock Text="System Cleaner Pro v1.0 - جاهز"
                      FontSize="12"/>
        </Border>

    </Grid>
</Window>
