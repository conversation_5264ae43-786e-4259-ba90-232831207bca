<Window x:Class="SystemCleanerPro.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:SystemCleanerPro"
        xmlns:viewmodels="clr-namespace:SystemCleanerPro.ViewModels"
        mc:Ignorable="d"
        Title="System Cleaner Pro - منظف النظام الاحترافي"
        Height="700" Width="1200"
        MinHeight="600" MinWidth="900"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.DataContext>
        <viewmodels:MainViewModel />
    </Window.DataContext>

    <Window.Resources>
        <!-- تعريف الألوان والأنماط -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="AccentBrush" Color="#FF4081"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>

        <!-- نمط الأزرار الرئيسية -->
        <Style x:Key="MainButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="50"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>

        <!-- نمط البطاقات -->
        <Style x:Key="CardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والتحكم -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource CardStyle}"
                            Background="{StaticResource PrimaryBrush}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="CleaningServices"
                                           Width="32" Height="32"
                                           Foreground="White"
                                           Margin="0,0,10,0"/>
                    <TextBlock Text="System Cleaner Pro"
                              FontSize="20"
                              FontWeight="Bold"
                              Foreground="White"
                              VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                            Command="{Binding ToggleLanguageCommand}"
                            ToolTip="تغيير اللغة / Change Language">
                        <materialDesign:PackIcon Kind="Translate" Foreground="White"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                            Command="{Binding ToggleThemeCommand}"
                            ToolTip="تغيير الثيم / Change Theme">
                        <materialDesign:PackIcon Kind="ThemeLightDark" Foreground="White"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                            Command="{Binding OpenSettingsCommand}"
                            ToolTip="الإعدادات / Settings">
                        <materialDesign:PackIcon Kind="Settings" Foreground="White"/>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- اللوحة اليسرى - الأدوات الرئيسية -->
            <Grid Grid.Column="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- بطاقة معلومات النظام -->
                <materialDesign:Card Grid.Row="0" Style="{StaticResource CardStyle}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- استخدام المعالج -->
                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Cpu64Bit" Width="32" Height="32"
                                                   Foreground="{StaticResource PrimaryBrush}"
                                                   HorizontalAlignment="Center"/>
                            <TextBlock Text="المعالج" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            <TextBlock Text="{Binding SystemInfo.CpuUsage, StringFormat={}{0:F1}%}"
                                      FontSize="18" FontWeight="Bold"
                                      HorizontalAlignment="Center"
                                      Foreground="{StaticResource PrimaryBrush}"/>
                        </StackPanel>

                        <!-- استخدام الذاكرة -->
                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Memory" Width="32" Height="32"
                                                   Foreground="{StaticResource AccentBrush}"
                                                   HorizontalAlignment="Center"/>
                            <TextBlock Text="الذاكرة" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            <TextBlock Text="{Binding SystemInfo.MemoryUsage, StringFormat={}{0:F1}%}"
                                      FontSize="18" FontWeight="Bold"
                                      HorizontalAlignment="Center"
                                      Foreground="{StaticResource AccentBrush}"/>
                        </StackPanel>

                        <!-- نقاط النظام -->
                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Speedometer" Width="32" Height="32"
                                                   Foreground="{StaticResource SuccessBrush}"
                                                   HorizontalAlignment="Center"/>
                            <TextBlock Text="نقاط النظام" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            <TextBlock Text="{Binding SystemInfo.SystemScore}"
                                      FontSize="18" FontWeight="Bold"
                                      HorizontalAlignment="Center"
                                      Foreground="{StaticResource SuccessBrush}"/>
                        </StackPanel>
                    </Grid>
                </materialDesign:Card>

                <!-- الأدوات الرئيسية -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- أزرار التنظيف الرئيسية -->
                        <materialDesign:Card Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="أدوات التنظيف الرئيسية"
                                          FontSize="16" FontWeight="Bold"
                                          Margin="0,0,0,15"/>

                                <UniformGrid Columns="2" Rows="2">
                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Command="{Binding QuickCleanCommand}"
                                            IsEnabled="{Binding CanStartCleaning}"
                                            Background="{StaticResource SuccessBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Flash" Width="20" Height="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="تنظيف سريع"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Command="{Binding CleanTempFilesCommand}"
                                            IsEnabled="{Binding CanStartCleaning}"
                                            Background="{StaticResource PrimaryBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FolderRemove" Width="20" Height="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="تنظيف الملفات المؤقتة"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Command="{Binding OptimizeMemoryCommand}"
                                            IsEnabled="{Binding CanStartCleaning}"
                                            Background="{StaticResource AccentBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Memory" Width="20" Height="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="تحسين الذاكرة"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Command="{Binding CleanRegistryCommand}"
                                            IsEnabled="{Binding CanStartCleaning}"
                                            Background="{StaticResource WarningBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Registry" Width="20" Height="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="تنظيف السجل"/>
                                        </StackPanel>
                                    </Button>
                                </UniformGrid>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- أدوات إضافية -->
                        <materialDesign:Card Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="أدوات إضافية"
                                          FontSize="16" FontWeight="Bold"
                                          Margin="0,0,0,15"/>

                                <UniformGrid Columns="2" Rows="2">
                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Command="{Binding OpenStartupManagerCommand}"
                                            Background="{StaticResource AccentBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Rocket" Width="20" Height="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="إدارة برامج الإقلاع"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Background="{StaticResource PrimaryBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="ContentDuplicate" Width="20" Height="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="البحث عن المكررات"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Command="{Binding CleanBrowsersCommand}"
                                            IsEnabled="{Binding CanStartCleaning}"
                                            Background="{StaticResource SuccessBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Web" Width="20" Height="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="تنظيف المتصفحات"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Background="{StaticResource WarningBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="HardDisk" Width="20" Height="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="تحليل القرص"/>
                                        </StackPanel>
                                    </Button>
                                </UniformGrid>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </ScrollViewer>
            </Grid>

            <!-- اللوحة اليمنى - المعلومات والتقارير -->
            <Grid Grid.Column="1" Margin="10,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- بطاقة الحالة الحالية -->
                <materialDesign:Card Grid.Row="0" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="حالة النظام"
                                  FontSize="16" FontWeight="Bold"
                                  Margin="0,0,0,10"/>

                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <materialDesign:PackIcon Kind="Computer" Width="16" Height="16"
                                                   VerticalAlignment="Center" Margin="0,0,5,0"/>
                            <TextBlock Text="{Binding SystemInfo.ComputerName}"
                                      VerticalAlignment="Center"/>
                        </StackPanel>

                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <materialDesign:PackIcon Kind="MicrosoftWindows" Width="16" Height="16"
                                                   VerticalAlignment="Center" Margin="0,0,5,0"/>
                            <TextBlock Text="{Binding SystemInfo.OSVersion}"
                                      VerticalAlignment="Center"/>
                        </StackPanel>

                        <Separator Margin="0,10"/>

                        <!-- مؤشر التقدم -->
                        <StackPanel Visibility="{Binding IsCleaningInProgress, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <TextBlock Text="جاري المعالجة..."
                                      FontWeight="Bold"
                                      HorizontalAlignment="Center"/>
                            <ProgressBar IsIndeterminate="True"
                                        Height="4"
                                        Margin="0,5"/>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- تقارير التنظيف -->
                <materialDesign:Card Grid.Row="1" Style="{StaticResource CardStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="تقارير التنظيف"
                                  FontSize="16" FontWeight="Bold"
                                  Margin="0,0,0,10"/>

                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                            <ItemsControl ItemsSource="{Binding CleaningResults}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <materialDesign:Card Margin="0,5" Padding="10"
                                                           materialDesign:ShadowAssist.ShadowDepth="Depth1">
                                            <StackPanel>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBlock Grid.Column="0" Text="{Binding CleaningType}"
                                                              FontWeight="Bold"/>
                                                    <TextBlock Grid.Column="1" Text="{Binding CleaningDate, StringFormat=HH:mm}"
                                                              FontSize="10"
                                                              Foreground="Gray"/>
                                                </Grid>

                                                <TextBlock Text="{Binding TotalFilesDeleted, StringFormat=الملفات المحذوفة: {0}}"
                                                          FontSize="11"
                                                          Margin="0,2,0,0"/>
                                                <TextBlock FontSize="11"
                                                          Margin="0,2,0,0">
                                                    <Run Text="المساحة المحررة: "/>
                                                    <Run Text="{Binding Path=., Converter={StaticResource SpaceFormatterConverter}}"
                                                         FontWeight="Bold"/>
                                                </TextBlock>
                                            </StackPanel>
                                        </materialDesign:Card>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </materialDesign:Card>
            </Grid>
        </Grid>

        <!-- شريط الحالة -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource CardStyle}"
                            Background="{DynamicResource MaterialDesignDarkBackground}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Information"
                                           Width="16" Height="16"
                                           Foreground="{DynamicResource MaterialDesignDarkForeground}"
                                           Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding StatusMessage}"
                              Foreground="{DynamicResource MaterialDesignDarkForeground}"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="System Cleaner Pro v1.0"
                              FontSize="10"
                              Foreground="{DynamicResource MaterialDesignDarkForeground}"
                              VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

    </Grid>
</Window>
