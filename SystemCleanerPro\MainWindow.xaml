<Window x:Class="SystemCleanerPro.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SystemCleanerPro"
        xmlns:viewmodels="clr-namespace:SystemCleanerPro.ViewModels"
        mc:Ignorable="d"
        Title="System Cleaner Pro - منظف النظام الاحترافي" 
        Height="700" Width="1200"
        MinHeight="600" MinWidth="900"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5">

    <Window.DataContext>
        <viewmodels:MainViewModel />
    </Window.DataContext>

    <Window.Resources>
        <!-- تعريف الألوان والأنماط -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="AccentBrush" Color="#FF4081"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        
        <!-- نمط الأزرار الرئيسية -->
        <Style x:Key="MainButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="50"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- نمط البطاقات -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان والتحكم -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🧹" FontSize="32" Margin="0,0,10,0"/>
                    <TextBlock Text="System Cleaner Pro - منظف النظام الاحترافي" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="White" 
                              VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="🌐" 
                            Background="Transparent" 
                            Foreground="White" 
                            BorderThickness="0"
                            FontSize="16"
                            Margin="5"
                            ToolTip="تغيير اللغة"
                            Command="{Binding ToggleLanguageCommand}"/>
                    <Button Content="🎨" 
                            Background="Transparent" 
                            Foreground="White" 
                            BorderThickness="0"
                            FontSize="16"
                            Margin="5"
                            ToolTip="تغيير الثيم"
                            Command="{Binding ToggleThemeCommand}"/>
                    <Button Content="⚙️" 
                            Background="Transparent" 
                            Foreground="White" 
                            BorderThickness="0"
                            FontSize="16"
                            Margin="5"
                            ToolTip="الإعدادات"
                            Command="{Binding OpenSettingsCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- اللوحة اليسرى - الأدوات الرئيسية -->
            <Grid Grid.Column="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- بطاقة معلومات النظام -->
                <Border Grid.Row="0" Style="{StaticResource CardStyle}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- استخدام المعالج -->
                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                            <TextBlock Text="💻" FontSize="32" HorizontalAlignment="Center"/>
                            <TextBlock Text="المعالج" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            <TextBlock Text="{Binding SystemInfo.CpuUsage, StringFormat={}{0:F1}%}" 
                                      FontSize="18" FontWeight="Bold" 
                                      HorizontalAlignment="Center"
                                      Foreground="{StaticResource PrimaryBrush}"/>
                        </StackPanel>

                        <!-- استخدام الذاكرة -->
                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                            <TextBlock Text="🧠" FontSize="32" HorizontalAlignment="Center"/>
                            <TextBlock Text="الذاكرة" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            <TextBlock Text="{Binding SystemInfo.MemoryUsage, StringFormat={}{0:F1}%}" 
                                      FontSize="18" FontWeight="Bold" 
                                      HorizontalAlignment="Center"
                                      Foreground="{StaticResource AccentBrush}"/>
                        </StackPanel>

                        <!-- نقاط النظام -->
                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                            <TextBlock Text="⚡" FontSize="32" HorizontalAlignment="Center"/>
                            <TextBlock Text="نقاط النظام" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                            <TextBlock Text="{Binding SystemInfo.SystemScore}" 
                                      FontSize="18" FontWeight="Bold" 
                                      HorizontalAlignment="Center"
                                      Foreground="{StaticResource SuccessBrush}"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- الأدوات الرئيسية -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- أزرار التنظيف الرئيسية -->
                        <Border Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="أدوات التنظيف الرئيسية" 
                                          FontSize="16" FontWeight="Bold" 
                                          Margin="0,0,0,15"/>
                                
                                <UniformGrid Columns="2" Rows="2">
                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Command="{Binding QuickCleanCommand}"
                                            IsEnabled="{Binding CanStartCleaning}"
                                            Background="{StaticResource SuccessBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="⚡" FontSize="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="تنظيف سريع"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Command="{Binding CleanTempFilesCommand}"
                                            IsEnabled="{Binding CanStartCleaning}"
                                            Background="{StaticResource PrimaryBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="📁" FontSize="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="تنظيف الملفات المؤقتة"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Command="{Binding OptimizeMemoryCommand}"
                                            IsEnabled="{Binding CanStartCleaning}"
                                            Background="{StaticResource AccentBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🧠" FontSize="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="تحسين الذاكرة"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Command="{Binding CleanRegistryCommand}"
                                            IsEnabled="{Binding CanStartCleaning}"
                                            Background="{StaticResource WarningBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="📋" FontSize="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="تنظيف السجل"/>
                                        </StackPanel>
                                    </Button>
                                </UniformGrid>
                            </StackPanel>
                        </Border>

                        <!-- أدوات إضافية -->
                        <Border Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="أدوات إضافية" 
                                          FontSize="16" FontWeight="Bold" 
                                          Margin="0,0,0,15"/>
                                
                                <UniformGrid Columns="2" Rows="2">
                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Command="{Binding OpenStartupManagerCommand}"
                                            Background="{StaticResource AccentBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🚀" FontSize="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="إدارة برامج الإقلاع"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Background="{StaticResource PrimaryBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="📄" FontSize="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="البحث عن المكررات"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Command="{Binding CleanBrowsersCommand}"
                                            IsEnabled="{Binding CanStartCleaning}"
                                            Background="{StaticResource SuccessBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🌐" FontSize="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="تنظيف المتصفحات"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Style="{StaticResource MainButtonStyle}"
                                            Background="{StaticResource WarningBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="💾" FontSize="20" Margin="0,0,5,0"/>
                                            <TextBlock Text="تحليل القرص"/>
                                        </StackPanel>
                                    </Button>
                                </UniformGrid>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </Grid>

            <!-- اللوحة اليمنى - المعلومات والتقارير -->
            <Grid Grid.Column="1" Margin="10,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- بطاقة الحالة الحالية -->
                <Border Grid.Row="0" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="حالة النظام" 
                                  FontSize="16" FontWeight="Bold" 
                                  Margin="0,0,0,10"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <TextBlock Text="💻" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="{Binding SystemInfo.ComputerName}" 
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <TextBlock Text="🪟" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="{Binding SystemInfo.OSVersion}" 
                                      VerticalAlignment="Center"/>
                        </StackPanel>

                        <Separator Margin="0,10"/>

                        <!-- مؤشر التقدم -->
                        <StackPanel Visibility="{Binding IsCleaningInProgress, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <TextBlock Text="جاري المعالجة..." 
                                      FontWeight="Bold" 
                                      HorizontalAlignment="Center"/>
                            <ProgressBar IsIndeterminate="True" 
                                        Height="4" 
                                        Margin="0,5"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- تقارير التنظيف -->
                <Border Grid.Row="1" Style="{StaticResource CardStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="تقارير التنظيف" 
                                  FontSize="16" FontWeight="Bold" 
                                  Margin="0,0,0,10"/>

                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                            <ItemsControl ItemsSource="{Binding CleaningResults}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="#F8F9FA" 
                                               CornerRadius="5" 
                                               Margin="0,5" 
                                               Padding="10">
                                            <StackPanel>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>
                                                    
                                                    <TextBlock Grid.Column="0" Text="{Binding CleaningType}" 
                                                              FontWeight="Bold"/>
                                                    <TextBlock Grid.Column="1" Text="{Binding CleaningDate, StringFormat=HH:mm}" 
                                                              FontSize="10" 
                                                              Foreground="Gray"/>
                                                </Grid>
                                                
                                                <TextBlock Text="{Binding TotalFilesDeleted, StringFormat=الملفات المحذوفة: {0}}" 
                                                          FontSize="11" 
                                                          Margin="0,2,0,0"/>
                                                <TextBlock FontSize="11" 
                                                          Margin="0,2,0,0">
                                                    <Run Text="المساحة المحررة: "/>
                                                    <Run Text="{Binding Path=., Converter={StaticResource SpaceFormatterConverter}}" 
                                                         FontWeight="Bold"/>
                                                </TextBlock>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </Border>
            </Grid>
        </Grid>

        <!-- شريط الحالة -->
        <Border Grid.Row="2" Background="#343A40" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="ℹ️" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding StatusMessage}" 
                              Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="System Cleaner Pro v1.0" 
                              FontSize="10" 
                              Foreground="White" 
                              VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

    </Grid>
</Window>
