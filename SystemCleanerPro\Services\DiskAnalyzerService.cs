using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace SystemCleanerPro.Services
{
    /// <summary>
    /// خدمة تحليل القرص الصلب
    /// </summary>
    public class DiskAnalyzerService
    {
        public event EventHandler<string>? ProgressUpdated;

        public async Task<DiskAnalysisResult> AnalyzeDiskAsync(string drivePath)
        {
            var result = new DiskAnalysisResult
            {
                DrivePath = drivePath,
                AnalysisDate = DateTime.Now
            };

            try
            {
                ProgressUpdated?.Invoke(this, $"تحليل القرص {drivePath}...");

                var driveInfo = new DriveInfo(drivePath);
                result.TotalSize = driveInfo.TotalSize;
                result.FreeSpace = driveInfo.AvailableFreeSpace;
                result.UsedSpace = result.TotalSize - result.FreeSpace;

                // تحليل المجلدات الرئيسية
                result.DirectoryItems = await AnalyzeDirectoriesAsync(driveInfo.RootDirectory);

                // العثور على أكبر الملفات
                result.LargestFiles = await FindLargestFilesAsync(driveInfo.RootDirectory, 20);

                result.IsSuccessful = true;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = ex.Message;
                result.IsSuccessful = false;
            }

            return result;
        }

        private async Task<List<DirectoryItem>> AnalyzeDirectoriesAsync(DirectoryInfo rootDirectory)
        {
            var directoryItems = new List<DirectoryItem>();

            await Task.Run(() =>
            {
                try
                {
                    foreach (var directory in rootDirectory.GetDirectories())
                    {
                        try
                        {
                            ProgressUpdated?.Invoke(this, $"تحليل المجلد: {directory.Name}");

                            var item = new DirectoryItem
                            {
                                Name = directory.Name,
                                Path = directory.FullName,
                                Size = CalculateDirectorySize(directory),
                                FileCount = CountFiles(directory),
                                LastModified = directory.LastWriteTime
                            };

                            directoryItems.Add(item);
                        }
                        catch (UnauthorizedAccessException)
                        {
                            // تجاهل المجلدات المحمية
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"خطأ في تحليل المجلد {directory.Name}: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحليل المجلدات: {ex.Message}");
                }
            });

            return directoryItems.OrderByDescending(d => d.Size).ToList();
        }

        private long CalculateDirectorySize(DirectoryInfo directory)
        {
            long size = 0;

            try
            {
                // حساب حجم الملفات في المجلد الحالي
                foreach (var file in directory.GetFiles())
                {
                    try
                    {
                        size += file.Length;
                    }
                    catch
                    {
                        // تجاهل الملفات المحمية
                    }
                }

                // حساب حجم المجلدات الفرعية
                foreach (var subDirectory in directory.GetDirectories())
                {
                    try
                    {
                        size += CalculateDirectorySize(subDirectory);
                    }
                    catch
                    {
                        // تجاهل المجلدات المحمية
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء الوصول
            }

            return size;
        }

        private int CountFiles(DirectoryInfo directory)
        {
            int count = 0;

            try
            {
                count += directory.GetFiles().Length;

                foreach (var subDirectory in directory.GetDirectories())
                {
                    try
                    {
                        count += CountFiles(subDirectory);
                    }
                    catch
                    {
                        // تجاهل المجلدات المحمية
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء الوصول
            }

            return count;
        }

        private async Task<List<FileItem>> FindLargestFilesAsync(DirectoryInfo rootDirectory, int maxFiles)
        {
            var largestFiles = new List<FileItem>();

            await Task.Run(() =>
            {
                try
                {
                    FindLargestFilesRecursive(rootDirectory, largestFiles, maxFiles * 2); // جمع ضعف العدد ثم ترتيب
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن أكبر الملفات: {ex.Message}");
                }
            });

            return largestFiles.OrderByDescending(f => f.Size).Take(maxFiles).ToList();
        }

        private void FindLargestFilesRecursive(DirectoryInfo directory, List<FileItem> largestFiles, int maxFiles)
        {
            try
            {
                // إضافة الملفات من المجلد الحالي
                foreach (var file in directory.GetFiles())
                {
                    try
                    {
                        if (file.Length > 10 * 1024 * 1024) // فقط الملفات أكبر من 10 ميجابايت
                        {
                            largestFiles.Add(new FileItem
                            {
                                Name = file.Name,
                                Path = file.FullName,
                                Size = file.Length,
                                Extension = file.Extension,
                                LastModified = file.LastWriteTime
                            });

                            // الاحتفاظ بأكبر الملفات فقط لتوفير الذاكرة
                            if (largestFiles.Count > maxFiles)
                            {
                                largestFiles = largestFiles.OrderByDescending(f => f.Size).Take(maxFiles).ToList();
                            }
                        }
                    }
                    catch
                    {
                        // تجاهل الملفات المحمية
                    }
                }

                // البحث في المجلدات الفرعية
                foreach (var subDirectory in directory.GetDirectories())
                {
                    try
                    {
                        if (!IsSystemDirectory(subDirectory.Name))
                        {
                            FindLargestFilesRecursive(subDirectory, largestFiles, maxFiles);
                        }
                    }
                    catch
                    {
                        // تجاهل المجلدات المحمية
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء الوصول
            }
        }

        private bool IsSystemDirectory(string directoryName)
        {
            var systemDirectories = new[]
            {
                "System Volume Information", "$Recycle.Bin", "Recovery",
                "System32", "SysWOW64", "Windows"
            };

            return systemDirectories.Any(sysDir => 
                directoryName.Equals(sysDir, StringComparison.OrdinalIgnoreCase));
        }

        public List<DriveInfo> GetAvailableDrives()
        {
            return DriveInfo.GetDrives()
                           .Where(d => d.IsReady && d.DriveType == DriveType.Fixed)
                           .ToList();
        }
    }

    /// <summary>
    /// نتيجة تحليل القرص
    /// </summary>
    public class DiskAnalysisResult
    {
        public string DrivePath { get; set; } = string.Empty;
        public DateTime AnalysisDate { get; set; }
        public long TotalSize { get; set; }
        public long FreeSpace { get; set; }
        public long UsedSpace { get; set; }
        public List<DirectoryItem> DirectoryItems { get; set; } = new List<DirectoryItem>();
        public List<FileItem> LargestFiles { get; set; } = new List<FileItem>();
        public bool IsSuccessful { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;

        public double UsagePercentage => TotalSize > 0 ? (double)UsedSpace / TotalSize * 100 : 0;
    }

    /// <summary>
    /// عنصر مجلد
    /// </summary>
    public class DirectoryItem
    {
        public string Name { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public long Size { get; set; }
        public int FileCount { get; set; }
        public DateTime LastModified { get; set; }

        public string GetFormattedSize()
        {
            if (Size < 1024)
                return $"{Size} B";
            else if (Size < 1024 * 1024)
                return $"{Size / 1024.0:F2} KB";
            else if (Size < 1024 * 1024 * 1024)
                return $"{Size / (1024.0 * 1024.0):F2} MB";
            else
                return $"{Size / (1024.0 * 1024.0 * 1024.0):F2} GB";
        }
    }

    /// <summary>
    /// عنصر ملف
    /// </summary>
    public class FileItem
    {
        public string Name { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public long Size { get; set; }
        public string Extension { get; set; } = string.Empty;
        public DateTime LastModified { get; set; }

        public string GetFormattedSize()
        {
            if (Size < 1024)
                return $"{Size} B";
            else if (Size < 1024 * 1024)
                return $"{Size / 1024.0:F2} KB";
            else if (Size < 1024 * 1024 * 1024)
                return $"{Size / (1024.0 * 1024.0):F2} MB";
            else
                return $"{Size / (1024.0 * 1024.0 * 1024.0):F2} GB";
        }
    }
}
