{"version": 3, "targets": {"net9.0-windows7.0": {"LiveCharts/0.9.7": {"type": "package", "compile": {"lib/net45/LiveCharts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/LiveCharts.dll": {"related": ".pdb;.xml"}}}, "LiveCharts.Wpf/0.9.7": {"type": "package", "dependencies": {"LiveCharts": "0.9.7"}, "compile": {"lib/net45/LiveCharts.Wpf.dll": {"related": ".pdb;.XML"}}, "runtime": {"lib/net45/LiveCharts.Wpf.dll": {"related": ".pdb;.XML"}}}, "MaterialDesignColors/2.1.4": {"type": "package", "compile": {"lib/net7.0/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/net7.0/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignThemes/4.9.0": {"type": "package", "dependencies": {"MaterialDesignColors": "2.1.4", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net7.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "compile": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "System.CodeDom/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Management/8.0.0": {"type": "package", "dependencies": {"System.CodeDom": "8.0.0"}, "compile": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.AccessControl/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.ServiceProcess.ServiceController/8.0.0": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "8.0.0"}, "compile": {"lib/net8.0/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.ServiceProcess.ServiceController.dll": {"assetType": "runtime", "rid": "win"}}}, "TaskScheduler/2.10.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Diagnostics.EventLog": "5.0.0", "System.Security.AccessControl": "5.0.0"}, "compile": {"lib/net6.0-windows7.0/Microsoft.Win32.TaskScheduler.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows7.0/Microsoft.Win32.TaskScheduler.dll": {"related": ".xml"}}, "resource": {"lib/net6.0-windows7.0/de/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "de"}, "lib/net6.0-windows7.0/es/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "es"}, "lib/net6.0-windows7.0/fr/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "fr"}, "lib/net6.0-windows7.0/it/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "it"}, "lib/net6.0-windows7.0/pl/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "pl"}, "lib/net6.0-windows7.0/ru/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "ru"}, "lib/net6.0-windows7.0/zh-CN/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "zh-CN"}, "lib/net6.0-windows7.0/zh-Hant/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "zh-Han<PERSON>"}}}}, "net9.0-windows7.0/win-arm64": {"LiveCharts/0.9.7": {"type": "package", "compile": {"lib/net45/LiveCharts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/LiveCharts.dll": {"related": ".pdb;.xml"}}}, "LiveCharts.Wpf/0.9.7": {"type": "package", "dependencies": {"LiveCharts": "0.9.7"}, "compile": {"lib/net45/LiveCharts.Wpf.dll": {"related": ".pdb;.XML"}}, "runtime": {"lib/net45/LiveCharts.Wpf.dll": {"related": ".pdb;.XML"}}}, "MaterialDesignColors/2.1.4": {"type": "package", "compile": {"lib/net7.0/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/net7.0/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignThemes/4.9.0": {"type": "package", "dependencies": {"MaterialDesignColors": "2.1.4", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net7.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "compile": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "System.CodeDom/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management/8.0.0": {"type": "package", "dependencies": {"System.CodeDom": "8.0.0"}, "compile": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.AccessControl/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"related": ".xml"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"related": ".xml"}}}, "System.ServiceProcess.ServiceController/8.0.0": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "8.0.0"}, "compile": {"lib/net8.0/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "TaskScheduler/2.10.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Diagnostics.EventLog": "5.0.0", "System.Security.AccessControl": "5.0.0"}, "compile": {"lib/net6.0-windows7.0/Microsoft.Win32.TaskScheduler.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows7.0/Microsoft.Win32.TaskScheduler.dll": {"related": ".xml"}}, "resource": {"lib/net6.0-windows7.0/de/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "de"}, "lib/net6.0-windows7.0/es/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "es"}, "lib/net6.0-windows7.0/fr/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "fr"}, "lib/net6.0-windows7.0/it/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "it"}, "lib/net6.0-windows7.0/pl/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "pl"}, "lib/net6.0-windows7.0/ru/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "ru"}, "lib/net6.0-windows7.0/zh-CN/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "zh-CN"}, "lib/net6.0-windows7.0/zh-Hant/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "zh-Han<PERSON>"}}}}, "net9.0-windows7.0/win-x64": {"LiveCharts/0.9.7": {"type": "package", "compile": {"lib/net45/LiveCharts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/LiveCharts.dll": {"related": ".pdb;.xml"}}}, "LiveCharts.Wpf/0.9.7": {"type": "package", "dependencies": {"LiveCharts": "0.9.7"}, "compile": {"lib/net45/LiveCharts.Wpf.dll": {"related": ".pdb;.XML"}}, "runtime": {"lib/net45/LiveCharts.Wpf.dll": {"related": ".pdb;.XML"}}}, "MaterialDesignColors/2.1.4": {"type": "package", "compile": {"lib/net7.0/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/net7.0/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignThemes/4.9.0": {"type": "package", "dependencies": {"MaterialDesignColors": "2.1.4", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net7.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "compile": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "System.CodeDom/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management/8.0.0": {"type": "package", "dependencies": {"System.CodeDom": "8.0.0"}, "compile": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.AccessControl/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"related": ".xml"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"related": ".xml"}}}, "System.ServiceProcess.ServiceController/8.0.0": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "8.0.0"}, "compile": {"lib/net8.0/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "TaskScheduler/2.10.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Diagnostics.EventLog": "5.0.0", "System.Security.AccessControl": "5.0.0"}, "compile": {"lib/net6.0-windows7.0/Microsoft.Win32.TaskScheduler.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows7.0/Microsoft.Win32.TaskScheduler.dll": {"related": ".xml"}}, "resource": {"lib/net6.0-windows7.0/de/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "de"}, "lib/net6.0-windows7.0/es/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "es"}, "lib/net6.0-windows7.0/fr/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "fr"}, "lib/net6.0-windows7.0/it/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "it"}, "lib/net6.0-windows7.0/pl/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "pl"}, "lib/net6.0-windows7.0/ru/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "ru"}, "lib/net6.0-windows7.0/zh-CN/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "zh-CN"}, "lib/net6.0-windows7.0/zh-Hant/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "zh-Han<PERSON>"}}}}, "net9.0-windows7.0/win-x86": {"LiveCharts/0.9.7": {"type": "package", "compile": {"lib/net45/LiveCharts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net45/LiveCharts.dll": {"related": ".pdb;.xml"}}}, "LiveCharts.Wpf/0.9.7": {"type": "package", "dependencies": {"LiveCharts": "0.9.7"}, "compile": {"lib/net45/LiveCharts.Wpf.dll": {"related": ".pdb;.XML"}}, "runtime": {"lib/net45/LiveCharts.Wpf.dll": {"related": ".pdb;.XML"}}}, "MaterialDesignColors/2.1.4": {"type": "package", "compile": {"lib/net7.0/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/net7.0/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignThemes/4.9.0": {"type": "package", "dependencies": {"MaterialDesignColors": "2.1.4", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net7.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "compile": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "System.CodeDom/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management/8.0.0": {"type": "package", "dependencies": {"System.CodeDom": "8.0.0"}, "compile": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.AccessControl/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"related": ".xml"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"related": ".xml"}}}, "System.ServiceProcess.ServiceController/8.0.0": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "8.0.0"}, "compile": {"lib/net8.0/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "TaskScheduler/2.10.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Diagnostics.EventLog": "5.0.0", "System.Security.AccessControl": "5.0.0"}, "compile": {"lib/net6.0-windows7.0/Microsoft.Win32.TaskScheduler.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows7.0/Microsoft.Win32.TaskScheduler.dll": {"related": ".xml"}}, "resource": {"lib/net6.0-windows7.0/de/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "de"}, "lib/net6.0-windows7.0/es/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "es"}, "lib/net6.0-windows7.0/fr/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "fr"}, "lib/net6.0-windows7.0/it/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "it"}, "lib/net6.0-windows7.0/pl/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "pl"}, "lib/net6.0-windows7.0/ru/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "ru"}, "lib/net6.0-windows7.0/zh-CN/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "zh-CN"}, "lib/net6.0-windows7.0/zh-Hant/Microsoft.Win32.TaskScheduler.resources.dll": {"locale": "zh-Han<PERSON>"}}}}}, "libraries": {"LiveCharts/0.9.7": {"sha512": "p4xyBrUaNNfkWZSNsIle0r51cyVyFg0Yi0uBRZM5GQtdFiA/7guZHlffFyNUILIdux10Ch2hUaJpcZENVU9aaQ==", "type": "package", "path": "livecharts/0.9.7", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/LiveCharts.dll", "lib/net40/LiveCharts.pdb", "lib/net40/LiveCharts.xml", "lib/net45/LiveCharts.dll", "lib/net45/LiveCharts.pdb", "lib/net45/LiveCharts.xml", "lib/portable-net45+win8+wp8/LiveCharts.XML", "lib/portable-net45+win8+wp8/LiveCharts.dll", "lib/portable-net45+win8+wp8/LiveCharts.pdb", "livecharts.0.9.7.nupkg.sha512", "livecharts.nuspec", "readme.txt"]}, "LiveCharts.Wpf/0.9.7": {"sha512": "lv6YZoK0B4yWykMq5b9grZXtIbTj+OWu4sVN9GDpdgV6zffu64m4d5CW8KSjfSl9rB8VU649x1NZvhoU67hCDg==", "type": "package", "path": "livecharts.wpf/0.9.7", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/LiveCharts.Wpf.XML", "lib/net40/LiveCharts.Wpf.dll", "lib/net40/LiveCharts.Wpf.pdb", "lib/net45/LiveCharts.Wpf.XML", "lib/net45/LiveCharts.Wpf.dll", "lib/net45/LiveCharts.Wpf.pdb", "livecharts.wpf.0.9.7.nupkg.sha512", "livecharts.wpf.nuspec", "tools/install.ps1"]}, "MaterialDesignColors/2.1.4": {"sha512": "C4Oy+qkjMoMPoZKyqYdCnIYtK8c0OSIHmNP73Vgc69NjiUG093xTkE7W/Ks54cTDS7fmWOtUHfwISTVTtb/YKg==", "type": "package", "path": "materialdesigncolors/2.1.4", "files": [".nupkg.metadata", ".signature.p7s", "images/MaterialDesignColors.Icon.png", "lib/net462/MaterialDesignColors.dll", "lib/net462/MaterialDesignColors.pdb", "lib/net6.0/MaterialDesignColors.dll", "lib/net6.0/MaterialDesignColors.pdb", "lib/net7.0/MaterialDesignColors.dll", "lib/net7.0/MaterialDesignColors.pdb", "lib/netcoreapp3.1/MaterialDesignColors.dll", "lib/netcoreapp3.1/MaterialDesignColors.pdb", "materialdesigncolors.2.1.4.nupkg.sha512", "materialdesigncolors.nuspec"]}, "MaterialDesignThemes/4.9.0": {"sha512": "Bp9Auw70j+9V7WsUMT4pc8ulVzfL0Eav/tyGgICDirxxhKJwhqtC/6PRkTUm+R1t9611xiDuk5pSUNdDV6vfOQ==", "type": "package", "path": "materialdesignthemes/4.9.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/MaterialDesignThemes.targets", "build/Resources/Roboto/Roboto-Black.ttf", "build/Resources/Roboto/Roboto-BlackItalic.ttf", "build/Resources/Roboto/Roboto-Bold.ttf", "build/Resources/Roboto/Roboto-BoldItalic.ttf", "build/Resources/Roboto/Roboto-Italic.ttf", "build/Resources/Roboto/Roboto-Light.ttf", "build/Resources/Roboto/Roboto-LightItalic.ttf", "build/Resources/Roboto/Roboto-Medium.ttf", "build/Resources/Roboto/Roboto-MediumItalic.ttf", "build/Resources/Roboto/Roboto-Regular.ttf", "build/Resources/Roboto/Roboto-Thin.ttf", "build/Resources/Roboto/Roboto-ThinItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Bold.ttf", "build/Resources/Roboto/RobotoCondensed-BoldItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Italic.ttf", "build/Resources/Roboto/RobotoCondensed-Light.ttf", "build/Resources/Roboto/RobotoCondensed-LightItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Regular.ttf", "images/MaterialDesignThemes.Icon.png", "lib/net462/MaterialDesignThemes.Wpf.dll", "lib/net462/MaterialDesignThemes.Wpf.pdb", "lib/net462/MaterialDesignThemes.Wpf.xml", "lib/net6.0/MaterialDesignThemes.Wpf.dll", "lib/net6.0/MaterialDesignThemes.Wpf.pdb", "lib/net6.0/MaterialDesignThemes.Wpf.xml", "lib/net7.0/MaterialDesignThemes.Wpf.dll", "lib/net7.0/MaterialDesignThemes.Wpf.pdb", "lib/net7.0/MaterialDesignThemes.Wpf.xml", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.dll", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.pdb", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.xml", "materialdesignthemes.4.9.0.nupkg.sha512", "materialdesignthemes.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "Microsoft.NETCore.Platforms/5.0.0": {"sha512": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "type": "package", "path": "microsoft.netcore.platforms/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.5.0.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.Registry/5.0.0": {"sha512": "dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "type": "package", "path": "microsoft.win32.registry/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.5.0.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"sha512": "8PZKqw9QOcu42xk8puY4P1+EXHL9YGOR9b7qhaYx5cILHul456H073tj99vyPcCt0W0781T9RwHqkx507ZyUpQ==", "type": "package", "path": "microsoft.xaml.behaviors.wpf/1.1.39", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Design/Microsoft.Xaml.Behaviors.Design.dll", "lib/net45/Microsoft.Xaml.Behaviors.dll", "lib/net45/Microsoft.Xaml.Behaviors.pdb", "lib/net45/Microsoft.Xaml.Behaviors.xml", "lib/net5.0-windows7.0/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.pdb", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.xml", "lib/netcoreapp3.1/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.dll", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.pdb", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.xml", "microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512", "microsoft.xaml.behaviors.wpf.nuspec", "tools/Install.ps1"]}, "System.CodeDom/8.0.0": {"sha512": "WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "type": "package", "path": "system.codedom/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/net8.0/System.CodeDom.dll", "lib/net8.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.8.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/8.0.0": {"sha512": "fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "type": "package", "path": "system.diagnostics.eventlog/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/net7.0/System.Diagnostics.EventLog.dll", "lib/net7.0/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.8.0.0.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/8.0.0": {"sha512": "jrK22i5LRzxZCfGb+tGmke2VH7oE0DvcDlJ1HAKYU8cPmD8XnpUT0bYn2Gy98GEhGjtfbR/sxKTVb+dE770pfA==", "type": "package", "path": "system.management/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/net8.0/System.Management.dll", "lib/net8.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "runtimes/win/lib/net8.0/System.Management.dll", "runtimes/win/lib/net8.0/System.Management.xml", "system.management.8.0.0.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.AccessControl/5.0.0": {"sha512": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "type": "package", "path": "system.security.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.5.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ServiceProcess.ServiceController/8.0.0": {"sha512": "jtYVG3bpw2n/NvNnP2g/JLri0D4UtfusTvLeH6cZPNAEjJXJVGspS3wLgVvjNbm+wjaYkFgsXejMTocV1T5DIQ==", "type": "package", "path": "system.serviceprocess.servicecontroller/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.ServiceProcess.ServiceController.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.ServiceProcess.ServiceController.targets", "lib/net462/System.ServiceProcess.ServiceController.dll", "lib/net462/System.ServiceProcess.ServiceController.xml", "lib/net6.0/System.ServiceProcess.ServiceController.dll", "lib/net6.0/System.ServiceProcess.ServiceController.xml", "lib/net7.0/System.ServiceProcess.ServiceController.dll", "lib/net7.0/System.ServiceProcess.ServiceController.xml", "lib/net8.0/System.ServiceProcess.ServiceController.dll", "lib/net8.0/System.ServiceProcess.ServiceController.xml", "lib/netstandard2.0/System.ServiceProcess.ServiceController.dll", "lib/netstandard2.0/System.ServiceProcess.ServiceController.xml", "runtimes/win/lib/net6.0/System.ServiceProcess.ServiceController.dll", "runtimes/win/lib/net6.0/System.ServiceProcess.ServiceController.xml", "runtimes/win/lib/net7.0/System.ServiceProcess.ServiceController.dll", "runtimes/win/lib/net7.0/System.ServiceProcess.ServiceController.xml", "runtimes/win/lib/net8.0/System.ServiceProcess.ServiceController.dll", "runtimes/win/lib/net8.0/System.ServiceProcess.ServiceController.xml", "system.serviceprocess.servicecontroller.8.0.0.nupkg.sha512", "system.serviceprocess.servicecontroller.nuspec", "useSharedDesignerContext.txt"]}, "TaskScheduler/2.10.1": {"sha512": "12b19oq9SOGbBq8745jQVQ08jAjgnsGXidQfclaZ94UZCtJRPkNfM8pz0D7pj04RHcQJQdZZnAUuC93reALqNg==", "type": "package", "path": "taskscheduler/2.10.1", "files": [".nupkg.metadata", ".signature.p7s", "TaskService.md", "lib/net20/Microsoft.Win32.TaskScheduler.dll", "lib/net20/Microsoft.Win32.TaskScheduler.xml", "lib/net20/de/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net20/es/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net20/fr/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net20/it/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net20/pl/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net20/ru/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net20/zh-CN/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net20/zh-Hant/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net35/Microsoft.Win32.TaskScheduler.dll", "lib/net35/Microsoft.Win32.TaskScheduler.xml", "lib/net35/de/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net35/es/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net35/fr/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net35/it/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net35/pl/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net35/ru/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net35/zh-CN/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net35/zh-Hant/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net40/Microsoft.Win32.TaskScheduler.dll", "lib/net40/Microsoft.Win32.TaskScheduler.xml", "lib/net40/de/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net40/es/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net40/fr/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net40/it/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net40/pl/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net40/ru/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net40/zh-CN/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net40/zh-Hant/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net452/Microsoft.Win32.TaskScheduler.dll", "lib/net452/Microsoft.Win32.TaskScheduler.xml", "lib/net452/de/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net452/es/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net452/fr/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net452/it/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net452/pl/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net452/ru/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net452/zh-CN/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net452/zh-Hant/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net5.0-windows7.0/Microsoft.Win32.TaskScheduler.dll", "lib/net5.0-windows7.0/Microsoft.Win32.TaskScheduler.xml", "lib/net5.0-windows7.0/de/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net5.0-windows7.0/es/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net5.0-windows7.0/fr/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net5.0-windows7.0/it/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net5.0-windows7.0/pl/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net5.0-windows7.0/ru/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net5.0-windows7.0/zh-CN/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net5.0-windows7.0/zh-Hant/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net6.0-windows7.0/Microsoft.Win32.TaskScheduler.dll", "lib/net6.0-windows7.0/Microsoft.Win32.TaskScheduler.xml", "lib/net6.0-windows7.0/de/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net6.0-windows7.0/es/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net6.0-windows7.0/fr/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net6.0-windows7.0/it/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net6.0-windows7.0/pl/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net6.0-windows7.0/ru/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net6.0-windows7.0/zh-CN/Microsoft.Win32.TaskScheduler.resources.dll", "lib/net6.0-windows7.0/zh-Hant/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.0/Microsoft.Win32.TaskScheduler.dll", "lib/netcoreapp2.0/Microsoft.Win32.TaskScheduler.xml", "lib/netcoreapp2.0/de/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.0/es/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.0/fr/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.0/it/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.0/pl/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.0/ru/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.0/zh-CN/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.0/zh-Hant/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.1/Microsoft.Win32.TaskScheduler.dll", "lib/netcoreapp2.1/Microsoft.Win32.TaskScheduler.xml", "lib/netcoreapp2.1/de/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.1/es/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.1/fr/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.1/it/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.1/pl/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.1/ru/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.1/zh-CN/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp2.1/zh-Hant/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.0/Microsoft.Win32.TaskScheduler.dll", "lib/netcoreapp3.0/Microsoft.Win32.TaskScheduler.xml", "lib/netcoreapp3.0/de/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.0/es/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.0/fr/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.0/it/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.0/pl/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.0/ru/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.0/zh-CN/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.0/zh-Hant/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.1/Microsoft.Win32.TaskScheduler.dll", "lib/netcoreapp3.1/Microsoft.Win32.TaskScheduler.xml", "lib/netcoreapp3.1/de/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.1/es/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.1/it/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.1/zh-CN/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netstandard2.0/Microsoft.Win32.TaskScheduler.dll", "lib/netstandard2.0/Microsoft.Win32.TaskScheduler.xml", "lib/netstandard2.0/de/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netstandard2.0/es/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netstandard2.0/fr/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netstandard2.0/it/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netstandard2.0/pl/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netstandard2.0/ru/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netstandard2.0/zh-CN/Microsoft.Win32.TaskScheduler.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Win32.TaskScheduler.resources.dll", "taskscheduler.2.10.1.nupkg.sha512", "taskscheduler.nuspec", "tsnew48.png"]}}, "projectFileDependencyGroups": {"net9.0-windows7.0": ["LiveCharts.Wpf >= 0.9.7", "MaterialDesignColors >= 2.1.4", "MaterialDesignThemes >= 4.9.0", "Microsoft.Win32.Registry >= 5.0.0", "System.Management >= 8.0.0", "System.ServiceProcess.ServiceController >= 8.0.0", "TaskScheduler >= 2.10.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\generationCleaning\\SystemCleanerPro\\SystemCleanerPro.csproj", "projectName": "SystemCleanerPro", "projectPath": "D:\\generationCleaning\\SystemCleanerPro\\SystemCleanerPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\generationCleaning\\SystemCleanerPro\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"LiveCharts.Wpf": {"target": "Package", "version": "[0.9.7, )"}, "MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.Win32.Registry": {"target": "Package", "version": "[5.0.0, )"}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}, "System.ServiceProcess.ServiceController": {"target": "Package", "version": "[8.0.0, )"}, "TaskScheduler": {"target": "Package", "version": "[2.10.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-arm64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x86", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.NETCore.App.Runtime.win-arm64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x86", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-arm64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x86", "version": "[9.0.6, 9.0.6]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}, "logs": [{"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Le package 'LiveCharts 0.9.7' a été restauré en utilisant '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' au lieu du framework cible du projet 'net9.0-windows7.0'. Ce package n'est peut-être pas totalement compatible avec votre projet.", "libraryId": "LiveCharts", "targetGraphs": ["net9.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Le package 'LiveCharts.Wpf 0.9.7' a été restauré en utilisant '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' au lieu du framework cible du projet 'net9.0-windows7.0'. Ce package n'est peut-être pas totalement compatible avec votre projet.", "libraryId": "LiveCharts.Wpf", "targetGraphs": ["net9.0-windows7.0"]}]}