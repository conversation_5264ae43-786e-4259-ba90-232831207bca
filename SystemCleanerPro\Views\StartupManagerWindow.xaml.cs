using System;
using System.Windows;
using SystemCleanerPro.ViewModels;

namespace SystemCleanerPro.Views
{
    /// <summary>
    /// نافذة إدارة برامج الإقلاع
    /// </summary>
    public partial class StartupManagerWindow : Window
    {
        private StartupManagerViewModel _viewModel;

        public StartupManagerWindow()
        {
            InitializeComponent();
            
            _viewModel = new StartupManagerViewModel();
            DataContext = _viewModel;
            
            _viewModel.CloseRequested += OnCloseRequested;
        }

        private void OnCloseRequested(object? sender, EventArgs e)
        {
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            if (_viewModel != null)
            {
                _viewModel.CloseRequested -= OnCloseRequested;
            }
            base.OnClosed(e);
        }
    }
}
