using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using SystemCleanerPro.Models;

namespace SystemCleanerPro.Services
{
    /// <summary>
    /// خدمة تنظيف المتصفحات
    /// </summary>
    public class BrowserCleanerService
    {
        private readonly Dictionary<string, BrowserInfo> _browsers;
        
        public event EventHandler<string>? ProgressUpdated;

        public BrowserCleanerService()
        {
            _browsers = InitializeBrowsers();
        }

        private Dictionary<string, BrowserInfo> InitializeBrowsers()
        {
            var userProfile = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
            var localAppData = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            var appData = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);

            return new Dictionary<string, BrowserInfo>
            {
                ["Chrome"] = new BrowserInfo
                {
                    Name = "Google Chrome",
                    CachePath = Path.Combine(localAppData, @"Google\Chrome\User Data\Default\Cache"),
                    CookiesPath = Path.Combine(localAppData, @"Google\Chrome\User Data\Default\Cookies"),
                    HistoryPath = Path.Combine(localAppData, @"Google\Chrome\User Data\Default\History"),
                    TempPath = Path.Combine(localAppData, @"Google\Chrome\User Data\Default\Local Storage")
                },
                ["Edge"] = new BrowserInfo
                {
                    Name = "Microsoft Edge",
                    CachePath = Path.Combine(localAppData, @"Microsoft\Edge\User Data\Default\Cache"),
                    CookiesPath = Path.Combine(localAppData, @"Microsoft\Edge\User Data\Default\Cookies"),
                    HistoryPath = Path.Combine(localAppData, @"Microsoft\Edge\User Data\Default\History"),
                    TempPath = Path.Combine(localAppData, @"Microsoft\Edge\User Data\Default\Local Storage")
                },
                ["Firefox"] = new BrowserInfo
                {
                    Name = "Mozilla Firefox",
                    CachePath = Path.Combine(localAppData, @"Mozilla\Firefox\Profiles"),
                    CookiesPath = Path.Combine(appData, @"Mozilla\Firefox\Profiles"),
                    HistoryPath = Path.Combine(appData, @"Mozilla\Firefox\Profiles"),
                    TempPath = Path.Combine(localAppData, @"Mozilla\Firefox\Profiles")
                },
                ["Opera"] = new BrowserInfo
                {
                    Name = "Opera",
                    CachePath = Path.Combine(appData, @"Opera Software\Opera Stable\Cache"),
                    CookiesPath = Path.Combine(appData, @"Opera Software\Opera Stable\Cookies"),
                    HistoryPath = Path.Combine(appData, @"Opera Software\Opera Stable\History"),
                    TempPath = Path.Combine(appData, @"Opera Software\Opera Stable\Local Storage")
                }
            };
        }

        public async Task<CleaningResult> CleanAllBrowsersAsync(BrowserCleaningOptions options)
        {
            var result = new CleaningResult
            {
                CleaningType = "تنظيف المتصفحات",
                CleaningDate = DateTime.Now
            };

            var startTime = DateTime.Now;
            long totalFilesDeleted = 0;
            long totalSpaceFreed = 0;

            try
            {
                foreach (var browser in _browsers.Values)
                {
                    ProgressUpdated?.Invoke(this, $"تنظيف {browser.Name}...");
                    
                    var browserResult = await CleanBrowserAsync(browser, options);
                    totalFilesDeleted += browserResult.FilesDeleted;
                    totalSpaceFreed += browserResult.SpaceFreed;
                    
                    if (browserResult.FilesDeleted > 0)
                    {
                        result.CleanedLocations.Add(browser.Name);
                    }
                }

                result.TotalFilesDeleted = totalFilesDeleted;
                result.TotalSpaceFreed = totalSpaceFreed;
                result.CleaningDuration = DateTime.Now - startTime;
                result.IsSuccessful = true;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في تنظيف المتصفحات: {ex.Message}");
                result.IsSuccessful = false;
            }

            return result;
        }

        private async Task<(long FilesDeleted, long SpaceFreed)> CleanBrowserAsync(BrowserInfo browser, BrowserCleaningOptions options)
        {
            long filesDeleted = 0;
            long spaceFreed = 0;

            try
            {
                // تنظيف الكاش
                if (options.CleanCache)
                {
                    var cacheResult = await CleanDirectoryAsync(browser.CachePath);
                    filesDeleted += cacheResult.FilesDeleted;
                    spaceFreed += cacheResult.SpaceFreed;
                }

                // تنظيف الكوكيز
                if (options.CleanCookies)
                {
                    var cookiesResult = await CleanBrowserFileAsync(browser.CookiesPath);
                    filesDeleted += cookiesResult.FilesDeleted;
                    spaceFreed += cookiesResult.SpaceFreed;
                }

                // تنظيف التاريخ
                if (options.CleanHistory)
                {
                    var historyResult = await CleanBrowserFileAsync(browser.HistoryPath);
                    filesDeleted += historyResult.FilesDeleted;
                    spaceFreed += historyResult.SpaceFreed;
                }

                // تنظيف التخزين المحلي
                if (options.CleanLocalStorage)
                {
                    var tempResult = await CleanDirectoryAsync(browser.TempPath);
                    filesDeleted += tempResult.FilesDeleted;
                    spaceFreed += tempResult.SpaceFreed;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف {browser.Name}: {ex.Message}");
            }

            return (filesDeleted, spaceFreed);
        }

        private async Task<(long FilesDeleted, long SpaceFreed)> CleanDirectoryAsync(string directoryPath)
        {
            long filesDeleted = 0;
            long spaceFreed = 0;

            await Task.Run(() =>
            {
                try
                {
                    if (Directory.Exists(directoryPath))
                    {
                        var files = Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories);
                        
                        foreach (var file in files)
                        {
                            try
                            {
                                var fileInfo = new FileInfo(file);
                                spaceFreed += fileInfo.Length;
                                File.Delete(file);
                                filesDeleted++;
                            }
                            catch
                            {
                                // تجاهل الملفات المحمية أو المستخدمة
                            }
                        }

                        // حذف المجلدات الفارغة
                        DeleteEmptyDirectories(directoryPath);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف المجلد {directoryPath}: {ex.Message}");
                }
            });

            return (filesDeleted, spaceFreed);
        }

        private async Task<(long FilesDeleted, long SpaceFreed)> CleanBrowserFileAsync(string filePath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    if (File.Exists(filePath))
                    {
                        var fileInfo = new FileInfo(filePath);
                        var size = fileInfo.Length;
                        File.Delete(filePath);
                        return (1L, size);
                    }
                }
                catch
                {
                    // تجاهل الأخطاء
                }

                return (0L, 0L);
            });
        }

        private void DeleteEmptyDirectories(string directoryPath)
        {
            try
            {
                var directories = Directory.GetDirectories(directoryPath, "*", SearchOption.AllDirectories)
                                           .OrderByDescending(d => d.Length);

                foreach (var dir in directories)
                {
                    try
                    {
                        if (!Directory.EnumerateFileSystemEntries(dir).Any())
                        {
                            Directory.Delete(dir);
                        }
                    }
                    catch
                    {
                        // تجاهل الأخطاء
                    }
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        public List<BrowserInfo> GetInstalledBrowsers()
        {
            var installedBrowsers = new List<BrowserInfo>();

            foreach (var browser in _browsers.Values)
            {
                if (IsBrowserInstalled(browser))
                {
                    installedBrowsers.Add(browser);
                }
            }

            return installedBrowsers;
        }

        private bool IsBrowserInstalled(BrowserInfo browser)
        {
            // فحص وجود مجلد المتصفح
            return Directory.Exists(Path.GetDirectoryName(browser.CachePath)) ||
                   Directory.Exists(Path.GetDirectoryName(browser.CookiesPath));
        }
    }

    /// <summary>
    /// معلومات المتصفح
    /// </summary>
    public class BrowserInfo
    {
        public string Name { get; set; } = string.Empty;
        public string CachePath { get; set; } = string.Empty;
        public string CookiesPath { get; set; } = string.Empty;
        public string HistoryPath { get; set; } = string.Empty;
        public string TempPath { get; set; } = string.Empty;
    }

    /// <summary>
    /// خيارات تنظيف المتصفح
    /// </summary>
    public class BrowserCleaningOptions
    {
        public bool CleanCache { get; set; } = true;
        public bool CleanCookies { get; set; } = false;
        public bool CleanHistory { get; set; } = false;
        public bool CleanLocalStorage { get; set; } = true;
    }
}
