using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace SystemCleanerPro.Models
{
    /// <summary>
    /// نموذج عنصر بدء التشغيل
    /// </summary>
    public class StartupItem : INotifyPropertyChanged
    {
        private bool _isEnabled;
        private string _name = string.Empty;
        private string _path = string.Empty;
        private string _location = string.Empty;
        private string _publisher = string.Empty;

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged();
            }
        }

        public string Path
        {
            get => _path;
            set
            {
                _path = value;
                OnPropertyChanged();
            }
        }

        public string Location
        {
            get => _location;
            set
            {
                _location = value;
                OnPropertyChanged();
            }
        }

        public string Publisher
        {
            get => _publisher;
            set
            {
                _publisher = value;
                OnPropertyChanged();
            }
        }

        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                _isEnabled = value;
                OnPropertyChanged();
            }
        }

        public StartupType Type { get; set; }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public enum StartupType
    {
        Registry,
        StartupFolder,
        Service,
        Task
    }
}
