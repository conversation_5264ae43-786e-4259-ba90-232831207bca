# دليل التثبيت - Installation Guide

## 📋 متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 7 SP1 أو أحدث
- **المعالج**: Intel/AMD 1 GHz أو أسرع
- **الذاكرة**: 512 MB RAM
- **المساحة**: 100 MB مساحة فارغة
- **الشاشة**: 1024x768 أو أعلى
- **إطار العمل**: .NET 8.0 Runtime

### المُوصى به
- **نظام التشغيل**: Windows 10/11
- **المعالج**: Intel/AMD 2 GHz أو أسرع
- **الذاكرة**: 2 GB RAM أو أكثر
- **المساحة**: 500 MB مساحة فارغة
- **الشاشة**: 1920x1080 أو أعلى

## 🔽 تحميل البرنامج

### الطريقة الأولى: الملفات المُجمعة (مُوصى بها)
1. انتقل إلى صفحة [Releases](https://github.com/your-repo/SystemCleanerPro/releases)
2. حمّل أحدث إصدار `SystemCleanerPro-v1.0.0-win-x64.zip`
3. استخرج الملفات إلى مجلد من اختيارك

### الطريقة الثانية: البناء من المصدر
```bash
# استنساخ المشروع
git clone https://github.com/your-repo/SystemCleanerPro.git

# الانتقال إلى مجلد المشروع
cd SystemCleanerPro

# بناء المشروع
dotnet publish -c Release -r win-x64 --self-contained
```

## ⚙️ تثبيت .NET Runtime

إذا لم يكن .NET 8.0 مثبتاً على نظامك:

### التحميل التلقائي
1. شغّل `SystemCleanerPro.exe`
2. إذا ظهرت رسالة خطأ، انقر على الرابط المُقترح
3. حمّل وثبّت .NET 8.0 Runtime

### التحميل اليدوي
1. انتقل إلى [صفحة تحميل .NET](https://dotnet.microsoft.com/download/dotnet/8.0)
2. حمّل "ASP.NET Core Runtime 8.0.x - Windows x64"
3. شغّل ملف التثبيت واتبع التعليمات

## 🚀 التثبيت والتشغيل

### التشغيل المباشر (بدون تثبيت)
1. استخرج ملفات البرنامج إلى مجلد
2. انقر بزر الماوس الأيمن على `SystemCleanerPro.exe`
3. اختر **"تشغيل كمدير"** (Run as Administrator)
4. اقبل طلب UAC

### إنشاء اختصار على سطح المكتب
1. انقر بزر الماوس الأيمن على `SystemCleanerPro.exe`
2. اختر **"إنشاء اختصار"** (Create Shortcut)
3. اسحب الاختصار إلى سطح المكتب
4. انقر بزر الماوس الأيمن على الاختصار
5. اختر **"خصائص"** (Properties)
6. في تبويب **"اختصار"**، انقر على **"متقدم"** (Advanced)
7. فعّل **"تشغيل كمدير"** (Run as Administrator)
8. انقر **"موافق"** مرتين

### إضافة إلى قائمة ابدأ
1. انسخ مجلد البرنامج إلى:
   ```
   C:\Program Files\SystemCleanerPro\
   ```
2. أنشئ اختصاراً في:
   ```
   C:\ProgramData\Microsoft\Windows\Start Menu\Programs\
   ```

## 🔧 التكوين الأولي

### التشغيل الأول
1. شغّل البرنامج كمدير
2. اختر اللغة المفضلة (عربي/إنجليزي)
3. اختر الثيم المفضل (فاتح/داكن)
4. راجع الإعدادات الافتراضية

### إعداد التنظيف التلقائي (اختياري)
1. افتح **الإعدادات**
2. انتقل إلى **"التنظيف التلقائي"**
3. فعّل الخيار وحدد التكرار المطلوب
4. اختر عمليات التنظيف المطلوبة
5. احفظ الإعدادات

## 🛡️ إعدادات الأمان

### Windows Defender
إذا حجب Windows Defender البرنامج:
1. افتح **Windows Security**
2. انتقل إلى **Virus & threat protection**
3. انقر على **Manage settings** تحت **Virus & threat protection settings**
4. أضف استثناء للمجلد أو الملف

### مكافح الفيروسات
لإضافة استثناء في برامج مكافحة الفيروسات الأخرى:
1. افتح برنامج مكافح الفيروسات
2. ابحث عن **"Exclusions"** أو **"Exceptions"**
3. أضف مجلد البرنامج أو الملف التنفيذي

### جدار الحماية
إذا طلب جدار الحماية إذناً:
1. اختر **"Allow access"** أو **"السماح بالوصول"**
2. تأكد من تحديد **"Private networks"** و **"Public networks"**

## 🔄 التحديث

### التحديث اليدوي
1. حمّل أحدث إصدار من GitHub
2. أغلق البرنامج الحالي
3. استبدل الملفات القديمة بالجديدة
4. احتفظ بملف الإعدادات إذا وُجد

### التحديث التلقائي (قريباً)
سيتم إضافة نظام التحديث التلقائي في الإصدارات القادمة.

## 🗑️ إلغاء التثبيت

### إزالة البرنامج
1. أغلق البرنامج تماماً
2. احذف مجلد البرنامج
3. احذف الاختصارات من سطح المكتب وقائمة ابدأ

### تنظيف البيانات المتبقية
1. احذف ملفات الإعدادات من:
   ```
   %APPDATA%\SystemCleanerPro\
   ```
2. احذف ملفات السجل من:
   ```
   %TEMP%\SystemCleanerPro.log
   ```

## ❗ حل المشاكل الشائعة

### "البرنامج لا يعمل"
- تأكد من تثبيت .NET 8.0 Runtime
- شغّل البرنامج كمدير
- تحقق من مكافح الفيروسات

### "خطأ في الوصول مرفوض"
- تأكد من تشغيل البرنامج كمدير
- تحقق من صلاحيات المجلد
- أغلق البرامج الأخرى التي قد تستخدم نفس الملفات

### "البرنامج بطيء"
- أغلق البرامج الأخرى
- تأكد من وجود مساحة كافية على القرص
- تحقق من عدم وجود فحص مكافح فيروسات نشط

### "لا تظهر بعض الملفات"
- هذا طبيعي للملفات المحمية من النظام
- تأكد من تشغيل البرنامج كمدير
- بعض الملفات قد تكون مستخدمة من برامج أخرى

## 📞 الحصول على المساعدة

إذا واجهت مشاكل في التثبيت:
1. راجع هذا الدليل مرة أخرى
2. تحقق من [الأسئلة الشائعة](FAQ.md)
3. ابحث في [GitHub Issues](https://github.com/your-repo/SystemCleanerPro/issues)
4. أنشئ تقرير خطأ جديد إذا لزم الأمر

---

**ملاحظة**: تأكد من إنشاء نسخة احتياطية من البيانات المهمة قبل استخدام البرنامج لأول مرة.
