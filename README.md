# System Cleaner Pro - منظف النظام الاحترافي

## نظرة عامة

System Cleaner Pro هو برنامج سطح مكتب احترافي وقوي مطور بلغة C# باستخدام WPF، مخصص لتنظيف وتحسين أداء الحاسوب. يتميز البرنامج بواجهة استخدام حديثة وعصرية تدعم اللغة العربية والإنجليزية، وهو متوافق مع Windows 7 فما فوق.

## ✨ الميزات الرئيسية

### 🧹 أدوات التنظيف الأساسية
- **تنظيف الملفات المؤقتة**: حذف ملفات Temp وملفات السجل من جميع المواقع
- **تحرير الرام**: تحسين استخدام الذاكرة باستخدام EmptyWorkingSet()
- **تنظيف السجل**: حذف المفاتيح الميتة مع إنشاء نقاط استعادة تلقائية
- **تنظيف المتصفحات**: حذف الكاش والكوكيز من Chrome, Edge, Firefox
- **التنظيف السريع**: تنظيف شامل بنقرة واحدة

### 📊 مراقبة الأداء
- **مراقبة لحظية**: عرض بياني حي لاستهلاك المعالج والرام
- **نظام التقييم**: تقييم حالة النظام من 0 إلى 100
- **التنبيهات الذكية**: إشعارات عند تجاوز نسب استهلاك محددة

### ⚙️ أدوات متقدمة
- **مدير برامج الإقلاع**: إدارة البرامج التي تعمل عند بدء التشغيل
- **باحث الملفات المكررة**: العثور على الملفات المكررة باستخدام Hash comparison
- **محلل القرص الصلب**: تحليل استخدام المساحة مع رسوم بيانية
- **الجدولة التلقائية**: تنظيف تلقائي مجدول مع الوضع الصامت

### 🎨 واجهة المستخدم
- **تصميم عصري**: واجهة Material Design جذابة ومتجاوبة
- **دعم متعدد اللغات**: العربية والإنجليزية
- **الوضع الليلي**: ثيمات فاتحة وداكنة
- **تقارير مفصلة**: عرض نتائج التنظيف مع الإحصائيات

## 🛠️ التقنيات المستخدمة

- **اللغة**: C# (.NET 8.0)
- **واجهة المستخدم**: WPF مع Material Design
- **المكتبات**:
  - MaterialDesignThemes
  - LiveCharts.Wpf
  - System.Management
  - Microsoft.Win32.Registry

## 📋 متطلبات النظام

- **نظام التشغيل**: Windows 7 أو أحدث
- **.NET Runtime**: .NET 8.0 أو أحدث
- **الذاكرة**: 512 MB RAM كحد أدنى
- **المساحة**: 100 MB مساحة فارغة
- **الصلاحيات**: صلاحيات المدير (للوصول إلى السجل ومجلدات النظام)

## 🚀 التثبيت والتشغيل

### التشغيل من المصدر
```bash
# استنساخ المشروع
git clone https://github.com/your-repo/SystemCleanerPro.git

# الانتقال إلى مجلد المشروع
cd SystemCleanerPro

# بناء المشروع
dotnet build

# تشغيل البرنامج
dotnet run
```

### بناء ملف تنفيذي
```bash
# بناء للنشر
dotnet publish -c Release -r win-x64 --self-contained

# الملف التنفيذي سيكون في:
# bin/Release/net8.0-windows/win-x64/publish/SystemCleanerPro.exe
```

## 📁 هيكل المشروع

```
SystemCleanerPro/
├── Models/                 # نماذج البيانات
│   ├── SystemInfo.cs
│   ├── CleaningResult.cs
│   └── StartupItem.cs
├── Services/              # الخدمات الأساسية
│   ├── SystemMonitorService.cs
│   ├── TempFileCleanerService.cs
│   ├── MemoryOptimizerService.cs
│   ├── RegistryCleanerService.cs
│   ├── BrowserCleanerService.cs
│   ├── StartupManagerService.cs
│   ├── DuplicateFinderService.cs
│   ├── DiskAnalyzerService.cs
│   └── SchedulerService.cs
├── ViewModels/            # ViewModels للـ MVVM
│   ├── MainViewModel.cs
│   ├── StartupManagerViewModel.cs
│   └── SettingsViewModel.cs
├── Views/                 # النوافذ والواجهات
│   ├── StartupManagerWindow.xaml
│   └── SettingsWindow.xaml
├── Utilities/             # الأدوات المساعدة
│   ├── RelayCommand.cs
│   └── Converters.cs
├── Resources/             # الموارد والأيقونات
├── Localization/          # ملفات الترجمة
└── Themes/               # ملفات الثيمات
```

## 🔧 الاستخدام

### التنظيف السريع
1. افتح البرنامج
2. انقر على "تنظيف سريع"
3. انتظر انتهاء العملية
4. راجع التقرير المفصل

### إدارة برامج الإقلاع
1. انقر على "إدارة برامج الإقلاع"
2. راجع قائمة البرامج
3. قم بتفعيل/تعطيل البرامج حسب الحاجة

### الجدولة التلقائية
1. افتح الإعدادات
2. فعّل "التنظيف التلقائي"
3. اختر التكرار المناسب
4. حدد عمليات التنظيف المطلوبة

## ⚠️ تحذيرات مهمة

- **تشغيل كمدير**: يتطلب البرنامج صلاحيات المدير للوصول إلى السجل ومجلدات النظام
- **نقاط الاستعادة**: يتم إنشاء نقاط استعادة تلقائياً قبل تنظيف السجل
- **النسخ الاحتياطي**: يُنصح بعمل نسخة احتياطية قبل التنظيف الشامل
- **الملفات الحساسة**: البرنامج يتجنب ملفات النظام الحرجة تلقائياً

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. تطبيق التغييرات مع التعليقات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الرسمي**: https://systemcleanerpro.com
- **التقارير**: استخدم GitHub Issues لتقارير الأخطاء

## 🔄 التحديثات

- **الإصدار الحالي**: 1.0.0
- **آخر تحديث**: ديسمبر 2024
- **التحديثات التلقائية**: قريباً

---

**ملاحظة**: هذا البرنامج مطور للأغراض التعليمية والاستخدام الشخصي. استخدمه على مسؤوليتك الخاصة.
