using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using SystemCleanerPro.Models;
using SystemCleanerPro.Services;
using SystemCleanerPro.Utilities;

namespace SystemCleanerPro.ViewModels
{
    /// <summary>
    /// ViewModel الرئيسي للتطبيق
    /// </summary>
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly SystemMonitorService _systemMonitor;
        private readonly TempFileCleanerService _tempFileCleaner;
        private readonly MemoryOptimizerService _memoryOptimizer;
        private readonly RegistryCleanerService _registryCleaner;
        private readonly StartupManagerService _startupManager;

        private SystemInfo _systemInfo;
        private bool _isCleaningInProgress;
        private string _statusMessage = "جاهز";
        private string _currentLanguage = "ar";
        private bool _isDarkMode = false;

        public MainViewModel()
        {
            _systemMonitor = new SystemMonitorService();
            _tempFileCleaner = new TempFileCleanerService();
            _memoryOptimizer = new MemoryOptimizerService();
            _registryCleaner = new RegistryCleanerService();
            _startupManager = new StartupManagerService();
            
            _systemInfo = new SystemInfo();
            CleaningResults = new ObservableCollection<CleaningResult>();

            InitializeCommands();
            InitializeServices();
        }

        #region Properties

        public SystemInfo SystemInfo
        {
            get => _systemInfo;
            set
            {
                _systemInfo = value;
                OnPropertyChanged();
            }
        }

        public bool IsCleaningInProgress
        {
            get => _isCleaningInProgress;
            set
            {
                _isCleaningInProgress = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CanStartCleaning));
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        public string CurrentLanguage
        {
            get => _currentLanguage;
            set
            {
                _currentLanguage = value;
                OnPropertyChanged();
            }
        }

        public bool IsDarkMode
        {
            get => _isDarkMode;
            set
            {
                _isDarkMode = value;
                OnPropertyChanged();
            }
        }

        public bool CanStartCleaning => !IsCleaningInProgress;

        public ObservableCollection<CleaningResult> CleaningResults { get; }

        #endregion

        #region Commands

        public ICommand CleanTempFilesCommand { get; private set; } = null!;
        public ICommand OptimizeMemoryCommand { get; private set; } = null!;
        public ICommand QuickCleanCommand { get; private set; } = null!;
        public ICommand CleanRegistryCommand { get; private set; } = null!;
        public ICommand OpenStartupManagerCommand { get; private set; } = null!;
        public ICommand ToggleLanguageCommand { get; private set; } = null!;
        public ICommand ToggleThemeCommand { get; private set; } = null!;

        #endregion

        private void InitializeCommands()
        {
            CleanTempFilesCommand = new RelayCommand(async () => await CleanTempFilesAsync(), () => CanStartCleaning);
            OptimizeMemoryCommand = new RelayCommand(async () => await OptimizeMemoryAsync(), () => CanStartCleaning);
            QuickCleanCommand = new RelayCommand(async () => await QuickCleanAsync(), () => CanStartCleaning);
            CleanRegistryCommand = new RelayCommand(async () => await CleanRegistryAsync(), () => CanStartCleaning);
            OpenStartupManagerCommand = new RelayCommand(OpenStartupManager);
            ToggleLanguageCommand = new RelayCommand(ToggleLanguage);
            ToggleThemeCommand = new RelayCommand(ToggleTheme);
        }

        private void InitializeServices()
        {
            _systemMonitor.SystemInfoUpdated += OnSystemInfoUpdated;
            _tempFileCleaner.ProgressUpdated += OnProgressUpdated;
            _memoryOptimizer.ProgressUpdated += OnProgressUpdated;
            _registryCleaner.ProgressUpdated += OnProgressUpdated;

            _systemMonitor.StartMonitoring();
        }

        private void OnSystemInfoUpdated(object? sender, SystemInfo systemInfo)
        {
            SystemInfo = systemInfo;
        }

        private void OnProgressUpdated(object? sender, string message)
        {
            StatusMessage = message;
        }

        private async Task CleanTempFilesAsync()
        {
            IsCleaningInProgress = true;
            StatusMessage = "جاري تنظيف الملفات المؤقتة...";

            try
            {
                var result = await _tempFileCleaner.CleanTempFilesAsync();
                CleaningResults.Insert(0, result);
                
                StatusMessage = result.IsSuccessful 
                    ? $"تم تنظيف {result.TotalFilesDeleted} ملف وتحرير {result.GetFormattedSpaceFreed()}"
                    : "فشل في تنظيف الملفات المؤقتة";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ: {ex.Message}";
            }
            finally
            {
                IsCleaningInProgress = false;
            }
        }

        private async Task OptimizeMemoryAsync()
        {
            IsCleaningInProgress = true;
            StatusMessage = "جاري تحسين الذاكرة...";

            try
            {
                var result = await _memoryOptimizer.OptimizeMemoryAsync();
                CleaningResults.Insert(0, result);
                
                StatusMessage = result.IsSuccessful 
                    ? $"تم تحرير {result.GetFormattedSpaceFreed()} من الذاكرة"
                    : "فشل في تحسين الذاكرة";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ: {ex.Message}";
            }
            finally
            {
                IsCleaningInProgress = false;
            }
        }

        private async Task QuickCleanAsync()
        {
            IsCleaningInProgress = true;
            StatusMessage = "جاري التنظيف السريع...";

            try
            {
                // تنظيف الملفات المؤقتة
                var tempResult = await _tempFileCleaner.CleanTempFilesAsync();
                
                // تحسين الذاكرة
                var memoryResult = await _memoryOptimizer.OptimizeMemoryAsync();

                // دمج النتائج
                var combinedResult = new CleaningResult
                {
                    CleaningType = "تنظيف سريع",
                    TotalFilesDeleted = tempResult.TotalFilesDeleted,
                    TotalSpaceFreed = tempResult.TotalSpaceFreed + memoryResult.TotalSpaceFreed,
                    CleaningDuration = tempResult.CleaningDuration + memoryResult.CleaningDuration,
                    IsSuccessful = tempResult.IsSuccessful && memoryResult.IsSuccessful
                };

                combinedResult.CleanedLocations.AddRange(tempResult.CleanedLocations);
                combinedResult.CleanedLocations.AddRange(memoryResult.CleanedLocations);

                CleaningResults.Insert(0, combinedResult);
                
                StatusMessage = combinedResult.IsSuccessful 
                    ? $"تم التنظيف السريع بنجاح - تحرير {combinedResult.GetFormattedSpaceFreed()}"
                    : "فشل في التنظيف السريع";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ: {ex.Message}";
            }
            finally
            {
                IsCleaningInProgress = false;
            }
        }

        private void ToggleLanguage()
        {
            CurrentLanguage = CurrentLanguage == "ar" ? "en" : "ar";
            // هنا يمكن إضافة منطق تغيير اللغة
        }

        private void ToggleTheme()
        {
            IsDarkMode = !IsDarkMode;
            // هنا يمكن إضافة منطق تغيير الثيم
        }

        private async Task CleanRegistryAsync()
        {
            IsCleaningInProgress = true;
            StatusMessage = "جاري تنظيف السجل...";

            try
            {
                var result = await _registryCleaner.CleanRegistryAsync();
                CleaningResults.Insert(0, result);

                StatusMessage = result.IsSuccessful
                    ? $"تم تنظيف {result.TotalFilesDeleted} مفتاح من السجل"
                    : "فشل في تنظيف السجل";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ: {ex.Message}";
            }
            finally
            {
                IsCleaningInProgress = false;
            }
        }

        private void OpenStartupManager()
        {
            try
            {
                var startupWindow = new SystemCleanerPro.Views.StartupManagerWindow();
                startupWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في فتح مدير برامج الإقلاع: {ex.Message}";
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            _systemMonitor?.Dispose();
        }
    }
}
