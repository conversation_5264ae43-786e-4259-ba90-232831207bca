using System;
using System.Diagnostics;
using System.Management;
using System.Threading;
using System.Threading.Tasks;
using SystemCleanerPro.Models;

namespace SystemCleanerPro.Services
{
    /// <summary>
    /// خدمة مراقبة النظام
    /// </summary>
    public class SystemMonitorService
    {
        private readonly PerformanceCounter _cpuCounter;
        private readonly PerformanceCounter _memoryCounter;
        private Timer? _monitoringTimer;
        private SystemInfo _systemInfo;

        public event EventHandler<SystemInfo>? SystemInfoUpdated;

        public SystemMonitorService()
        {
            _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
            _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
            _systemInfo = new SystemInfo();
            
            InitializeSystemInfo();
        }

        private void InitializeSystemInfo()
        {
            try
            {
                // الحصول على معلومات النظام الأساسية
                _systemInfo.ComputerName = Environment.MachineName;
                _systemInfo.OSVersion = Environment.OSVersion.ToString();
                
                // الحصول على إجمالي الذاكرة
                using (var searcher = new ManagementObjectSearcher("SELECT TotalPhysicalMemory FROM Win32_ComputerSystem"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        _systemInfo.TotalMemory = Convert.ToInt64(obj["TotalPhysicalMemory"]) / (1024 * 1024); // MB
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تهيئة معلومات النظام: {ex.Message}");
            }
        }

        public void StartMonitoring(int intervalMs = 1000)
        {
            _monitoringTimer = new Timer(UpdateSystemInfo, null, 0, intervalMs);
        }

        public void StopMonitoring()
        {
            _monitoringTimer?.Dispose();
            _monitoringTimer = null;
        }

        private void UpdateSystemInfo(object? state)
        {
            try
            {
                // تحديث استخدام المعالج
                _systemInfo.CpuUsage = Math.Round(_cpuCounter.NextValue(), 1);

                // تحديث استخدام الذاكرة
                var availableMemory = _memoryCounter.NextValue();
                _systemInfo.AvailableMemory = (long)availableMemory;
                
                if (_systemInfo.TotalMemory > 0)
                {
                    var usedMemory = _systemInfo.TotalMemory - availableMemory;
                    _systemInfo.MemoryUsage = Math.Round((usedMemory / _systemInfo.TotalMemory) * 100, 1);
                }

                // حساب نقاط النظام
                _systemInfo.SystemScore = CalculateSystemScore();

                // إرسال التحديث
                SystemInfoUpdated?.Invoke(this, _systemInfo);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"خطأ في تحديث معلومات النظام: {ex.Message}");
            }
        }

        private int CalculateSystemScore()
        {
            int score = 100;

            // تقليل النقاط بناءً على استخدام المعالج
            if (_systemInfo.CpuUsage > 80)
                score -= 30;
            else if (_systemInfo.CpuUsage > 60)
                score -= 20;
            else if (_systemInfo.CpuUsage > 40)
                score -= 10;

            // تقليل النقاط بناءً على استخدام الذاكرة
            if (_systemInfo.MemoryUsage > 90)
                score -= 25;
            else if (_systemInfo.MemoryUsage > 75)
                score -= 15;
            else if (_systemInfo.MemoryUsage > 60)
                score -= 10;

            return Math.Max(0, score);
        }

        public SystemInfo GetCurrentSystemInfo()
        {
            return _systemInfo;
        }

        public void Dispose()
        {
            StopMonitoring();
            _cpuCounter?.Dispose();
            _memoryCounter?.Dispose();
        }
    }
}
