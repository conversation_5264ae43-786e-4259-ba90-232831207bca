# Build Script for System Cleaner Pro
# سكريبت البناء لمنظف النظام الاحترافي

param(
    [string]$Configuration = "Release",
    [string]$Runtime = "win-x64",
    [switch]$SelfContained = $true,
    [switch]$SingleFile = $false,
    [switch]$Clean = $false
)

Write-Host "=== System Cleaner Pro Build Script ===" -ForegroundColor Cyan
Write-Host "Configuration: $Configuration" -ForegroundColor Yellow
Write-Host "Runtime: $Runtime" -ForegroundColor Yellow
Write-Host "Self-Contained: $SelfContained" -ForegroundColor Yellow
Write-Host "Single File: $SingleFile" -ForegroundColor Yellow

# تنظيف المجلدات السابقة
if ($Clean) {
    Write-Host "Cleaning previous builds..." -ForegroundColor Green
    Remove-Item -Path ".\SystemCleanerPro\bin" -Recurse -Force -ErrorAction SilentlyContinue
    Remove-Item -Path ".\SystemCleanerPro\obj" -Recurse -Force -ErrorAction SilentlyContinue
    Remove-Item -Path ".\publish" -Recurse -Force -ErrorAction SilentlyContinue
}

# التحقق من وجود .NET SDK
Write-Host "Checking .NET SDK..." -ForegroundColor Green
try {
    $dotnetVersion = dotnet --version
    Write-Host ".NET SDK Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: .NET SDK not found. Please install .NET 8.0 SDK." -ForegroundColor Red
    exit 1
}

# الانتقال إلى مجلد المشروع
Set-Location "SystemCleanerPro"

# استعادة الحزم
Write-Host "Restoring packages..." -ForegroundColor Green
dotnet restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "Error: Package restore failed." -ForegroundColor Red
    exit 1
}

# بناء المشروع
Write-Host "Building project..." -ForegroundColor Green
dotnet build -c $Configuration --no-restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "Error: Build failed." -ForegroundColor Red
    exit 1
}

# إعداد معاملات النشر
$publishArgs = @(
    "publish"
    "-c", $Configuration
    "-r", $Runtime
    "--no-build"
    "-o", "..\publish\$Runtime"
)

if ($SelfContained) {
    $publishArgs += "--self-contained"
} else {
    $publishArgs += "--no-self-contained"
}

if ($SingleFile) {
    $publishArgs += "-p:PublishSingleFile=true"
    $publishArgs += "-p:IncludeNativeLibrariesForSelfExtract=true"
}

# نشر المشروع
Write-Host "Publishing project..." -ForegroundColor Green
Write-Host "Command: dotnet $($publishArgs -join ' ')" -ForegroundColor Gray
& dotnet @publishArgs

if ($LASTEXITCODE -ne 0) {
    Write-Host "Error: Publish failed." -ForegroundColor Red
    exit 1
}

# العودة إلى المجلد الرئيسي
Set-Location ".."

# نسخ الملفات الإضافية
Write-Host "Copying additional files..." -ForegroundColor Green
$publishDir = "publish\$Runtime"

# نسخ ملفات التوثيق
Copy-Item "README.md" "$publishDir\" -Force
Copy-Item "LICENSE" "$publishDir\" -Force
Copy-Item "CHANGELOG.md" "$publishDir\" -Force
Copy-Item "USAGE.md" "$publishDir\" -Force
Copy-Item "INSTALL.md" "$publishDir\" -Force
Copy-Item "FAQ.md" "$publishDir\" -Force

# إنشاء ملف معلومات الإصدار
$versionInfo = @"
System Cleaner Pro - منظف النظام الاحترافي
Version: 1.0.0
Build Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Configuration: $Configuration
Runtime: $Runtime
Self-Contained: $SelfContained
Single File: $SingleFile

For more information, visit:
https://github.com/your-repo/SystemCleanerPro

© 2024 System Cleaner Pro. Licensed under MIT License.
"@

$versionInfo | Out-File -FilePath "$publishDir\VERSION.txt" -Encoding UTF8

# إنشاء ملف تشغيل سريع
$runScript = @"
@echo off
echo Starting System Cleaner Pro...
echo Please run as Administrator for full functionality.
echo.
SystemCleanerPro.exe
pause
"@

$runScript | Out-File -FilePath "$publishDir\Run.bat" -Encoding ASCII

# حساب حجم الملفات
$totalSize = (Get-ChildItem "$publishDir" -Recurse | Measure-Object -Property Length -Sum).Sum
$sizeInMB = [math]::Round($totalSize / 1MB, 2)

Write-Host "=== Build Completed Successfully ===" -ForegroundColor Green
Write-Host "Output Directory: $publishDir" -ForegroundColor Yellow
Write-Host "Total Size: $sizeInMB MB" -ForegroundColor Yellow
Write-Host "Files Count: $((Get-ChildItem "$publishDir" -Recurse -File).Count)" -ForegroundColor Yellow

# عرض الملفات الرئيسية
Write-Host "`nMain Files:" -ForegroundColor Cyan
Get-ChildItem "$publishDir" -File | Where-Object { $_.Extension -in @('.exe', '.dll', '.txt', '.md') } | 
    Select-Object Name, @{Name='Size (KB)'; Expression={[math]::Round($_.Length/1KB, 2)}} |
    Format-Table -AutoSize

Write-Host "Build completed! You can now run the application from: $publishDir\SystemCleanerPro.exe" -ForegroundColor Green
Write-Host "Remember to run as Administrator for full functionality." -ForegroundColor Yellow

# اختياري: فتح مجلد النتائج
$openFolder = Read-Host "Open output folder? (y/n)"
if ($openFolder -eq 'y' -or $openFolder -eq 'Y') {
    Start-Process "explorer.exe" -ArgumentList (Resolve-Path $publishDir)
}
