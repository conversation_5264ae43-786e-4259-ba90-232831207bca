using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using SystemCleanerPro.Models;

namespace SystemCleanerPro.Services
{
    /// <summary>
    /// خدمة تحسين الذاكرة
    /// </summary>
    public class MemoryOptimizerService
    {
        [DllImport("kernel32.dll")]
        private static extern bool SetProcessWorkingSetSize(IntPtr hProcess, int dwMinimumWorkingSetSize, int dwMaximumWorkingSetSize);

        [DllImport("psapi.dll")]
        private static extern bool EmptyWorkingSet(IntPtr hProcess);

        public event EventHandler<string>? ProgressUpdated;

        public async Task<CleaningResult> OptimizeMemoryAsync()
        {
            var result = new CleaningResult
            {
                CleaningType = "تحسين الذاكرة",
                CleaningDate = DateTime.Now
            };

            var startTime = DateTime.Now;
            long memoryFreedMB = 0;

            try
            {
                ProgressUpdated?.Invoke(this, "بدء تحسين الذاكرة...");

                // الحصول على استخدام الذاكرة قبل التحسين
                var memoryBefore = GC.GetTotalMemory(false);

                // تنظيف ذاكرة العملية الحالية
                ProgressUpdated?.Invoke(this, "تنظيف ذاكرة التطبيق الحالي...");
                await Task.Run(() =>
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                    
                    var currentProcess = Process.GetCurrentProcess();
                    EmptyWorkingSet(currentProcess.Handle);
                });

                // تحسين ذاكرة العمليات الأخرى (بحذر)
                ProgressUpdated?.Invoke(this, "تحسين ذاكرة العمليات الأخرى...");
                await OptimizeOtherProcessesAsync();

                // تنظيف الكاش
                ProgressUpdated?.Invoke(this, "تنظيف كاش النظام...");
                await ClearSystemCacheAsync();

                var memoryAfter = GC.GetTotalMemory(false);
                memoryFreedMB = (memoryBefore - memoryAfter) / (1024 * 1024);

                result.TotalSpaceFreed = memoryFreedMB * 1024 * 1024; // بالبايت
                result.CleaningDuration = DateTime.Now - startTime;
                result.IsSuccessful = true;
                result.CleanedLocations.Add("ذاكرة النظام");

                ProgressUpdated?.Invoke(this, $"تم تحرير {memoryFreedMB} ميجابايت من الذاكرة");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ في تحسين الذاكرة: {ex.Message}");
                result.IsSuccessful = false;
            }

            return result;
        }

        private async Task OptimizeOtherProcessesAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var processes = Process.GetProcesses();
                    int processedCount = 0;

                    foreach (var process in processes)
                    {
                        try
                        {
                            // تجنب العمليات الحرجة للنظام
                            if (IsSystemCriticalProcess(process.ProcessName))
                                continue;

                            // تحسين ذاكرة العملية
                            EmptyWorkingSet(process.Handle);
                            processedCount++;

                            // تحديث التقدم كل 10 عمليات
                            if (processedCount % 10 == 0)
                            {
                                ProgressUpdated?.Invoke(this, $"تم تحسين {processedCount} عملية...");
                            }
                        }
                        catch
                        {
                            // تجاهل الأخطاء للعمليات المحمية
                        }
                        finally
                        {
                            process.Dispose();
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"خطأ في تحسين العمليات: {ex.Message}");
                }
            });
        }

        private bool IsSystemCriticalProcess(string processName)
        {
            var criticalProcesses = new[]
            {
                "system", "smss", "csrss", "wininit", "winlogon", "services", "lsass",
                "svchost", "explorer", "dwm", "audiodg", "spoolsv", "taskhost",
                "conhost", "dllhost", "rundll32"
            };

            return Array.Exists(criticalProcesses, p => 
                processName.Equals(p, StringComparison.OrdinalIgnoreCase));
        }

        private async Task ClearSystemCacheAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    // تنظيف كاش .NET
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();

                    // محاولة تنظيف كاش النظام
                    SetProcessWorkingSetSize(Process.GetCurrentProcess().Handle, -1, -1);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"خطأ في تنظيف الكاش: {ex.Message}");
                }
            });
        }

        public long GetAvailableMemoryMB()
        {
            try
            {
                using (var pc = new PerformanceCounter("Memory", "Available MBytes"))
                {
                    return (long)pc.NextValue();
                }
            }
            catch
            {
                return 0;
            }
        }

        public double GetMemoryUsagePercentage()
        {
            try
            {
                using (var totalMemory = new PerformanceCounter("Memory", "Committed Bytes"))
                using (var availableMemory = new PerformanceCounter("Memory", "Available Bytes"))
                {
                    var total = totalMemory.NextValue();
                    var available = availableMemory.NextValue();
                    var used = total - available;
                    
                    return (used / total) * 100;
                }
            }
            catch
            {
                return 0;
            }
        }
    }
}
