<Window x:Class="SystemCleanerPro.Views.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="الإعدادات - Settings" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <materialDesign:Card Grid.Row="0" Margin="10" Padding="15" 
                            materialDesign:ShadowAssist.ShadowDepth="Depth2">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="Settings" 
                                       Width="32" Height="32" 
                                       Foreground="{StaticResource PrimaryBrush}" 
                                       VerticalAlignment="Center"/>
                <StackPanel Margin="15,0,0,0" VerticalAlignment="Center">
                    <TextBlock Text="الإعدادات" 
                              FontSize="18" FontWeight="Bold"/>
                    <TextBlock Text="تخصيص إعدادات البرنامج والتنظيف التلقائي" 
                              FontSize="12" 
                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>

        <!-- محتوى الإعدادات -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="10">
                
                <!-- إعدادات عامة -->
                <materialDesign:Card Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="الإعدادات العامة" 
                                  FontSize="16" FontWeight="Bold" 
                                  Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <CheckBox Content="تشغيل مع بدء النظام"
                                         IsChecked="{Binding StartWithWindows}"
                                         Margin="0,5"/>
                                <CheckBox Content="تصغير إلى شريط المهام"
                                         IsChecked="{Binding MinimizeToTray}"
                                         Margin="0,5"/>
                                <CheckBox Content="إظهار الإشعارات"
                                         IsChecked="{Binding ShowNotifications}"
                                         Margin="0,5"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="اللغة:" Margin="0,5,0,2"/>
                                <ComboBox SelectedValue="{Binding SelectedLanguage}"
                                         DisplayMemberPath="Name"
                                         SelectedValuePath="Code"
                                         ItemsSource="{Binding AvailableLanguages}"
                                         Margin="0,0,0,10"/>
                                
                                <TextBlock Text="الثيم:" Margin="0,5,0,2"/>
                                <ComboBox SelectedValue="{Binding SelectedTheme}"
                                         ItemsSource="{Binding AvailableThemes}"
                                         Margin="0,0,0,10"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- إعدادات التنظيف التلقائي -->
                <materialDesign:Card Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="التنظيف التلقائي" 
                                  FontSize="16" FontWeight="Bold" 
                                  Margin="0,0,0,15"/>
                        
                        <CheckBox Content="تفعيل التنظيف التلقائي"
                                 IsChecked="{Binding ScheduleSettings.IsEnabled}"
                                 FontWeight="Bold"
                                 Margin="0,0,0,10"/>

                        <Grid IsEnabled="{Binding ScheduleSettings.IsEnabled}">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="تكرار التنظيف:" Margin="0,5,0,2"/>
                                <ComboBox SelectedValue="{Binding ScheduleSettings.Frequency}"
                                         Margin="0,0,0,10">
                                    <ComboBoxItem Content="كل 5 دقائق" Tag="Every5Minutes"/>
                                    <ComboBoxItem Content="كل 30 دقيقة" Tag="Every30Minutes"/>
                                    <ComboBoxItem Content="كل ساعة" Tag="Hourly"/>
                                    <ComboBoxItem Content="يومياً" Tag="Daily"/>
                                    <ComboBoxItem Content="أسبوعياً" Tag="Weekly"/>
                                    <ComboBoxItem Content="مخصص" Tag="Custom"/>
                                </ComboBox>

                                <TextBlock Text="الفترة المخصصة (بالدقائق):" 
                                          Margin="0,5,0,2"
                                          Visibility="{Binding IsCustomFrequency, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                <TextBox Text="{Binding ScheduleSettings.CustomInterval}"
                                        Margin="0,0,0,10"
                                        Visibility="{Binding IsCustomFrequency, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="عمليات التنظيف:" Margin="0,5,0,5"/>
                                <CheckBox Content="تنظيف الملفات المؤقتة"
                                         IsChecked="{Binding ScheduleSettings.CleanTempFiles}"
                                         Margin="0,2"/>
                                <CheckBox Content="تحسين الذاكرة"
                                         IsChecked="{Binding ScheduleSettings.OptimizeMemory}"
                                         Margin="0,2"/>
                                <CheckBox Content="تنظيف السجل"
                                         IsChecked="{Binding ScheduleSettings.CleanRegistry}"
                                         Margin="0,2"/>
                                <CheckBox Content="الوضع الصامت"
                                         IsChecked="{Binding ScheduleSettings.SilentMode}"
                                         Margin="0,2"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- إعدادات التنبيهات -->
                <materialDesign:Card Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="التنبيهات والمراقبة" 
                                  FontSize="16" FontWeight="Bold" 
                                  Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="تنبيه عند تجاوز استخدام المعالج (%):" Margin="0,5,0,2"/>
                                <Slider Value="{Binding CpuAlertThreshold}"
                                       Minimum="50" Maximum="100"
                                       TickFrequency="10"
                                       IsSnapToTickEnabled="True"
                                       Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding CpuAlertThreshold, StringFormat={}{0}%}" 
                                          HorizontalAlignment="Center"
                                          FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="تنبيه عند تجاوز استخدام الذاكرة (%):" Margin="0,5,0,2"/>
                                <Slider Value="{Binding MemoryAlertThreshold}"
                                       Minimum="50" Maximum="100"
                                       TickFrequency="10"
                                       IsSnapToTickEnabled="True"
                                       Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding MemoryAlertThreshold, StringFormat={}{0}%}" 
                                          HorizontalAlignment="Center"
                                          FontSize="12"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <materialDesign:Card Grid.Row="2" Margin="10" Padding="15" 
                            materialDesign:ShadowAssist.ShadowDepth="Depth1">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Content="استعادة الافتراضي"
                        Command="{Binding RestoreDefaultsCommand}"
                        Margin="5,0"/>
                
                <Button Style="{StaticResource MaterialDesignRaisedButton}"
                        Content="حفظ"
                        Command="{Binding SaveCommand}"
                        Margin="5,0"/>
                
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Content="إلغاء"
                        Command="{Binding CancelCommand}"
                        Margin="5,0"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</Window>
