using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using SystemCleanerPro.Models;

namespace SystemCleanerPro.Services
{
    /// <summary>
    /// خدمة البحث عن الملفات المكررة
    /// </summary>
    public class DuplicateFinderService
    {
        public event EventHandler<string>? ProgressUpdated;

        public async Task<List<DuplicateFileGroup>> FindDuplicateFilesAsync(string[] searchPaths, long minFileSize = 1024)
        {
            var duplicateGroups = new List<DuplicateFileGroup>();
            var fileHashMap = new Dictionary<string, List<FileInfo>>();

            try
            {
                ProgressUpdated?.Invoke(this, "بدء البحث عن الملفات المكررة...");

                // الحصول على جميع الملفات
                var allFiles = new List<FileInfo>();
                foreach (var path in searchPaths)
                {
                    if (Directory.Exists(path))
                    {
                        ProgressUpdated?.Invoke(this, $"فحص المجلد: {path}");
                        var files = await GetFilesRecursivelyAsync(path, minFileSize);
                        allFiles.AddRange(files);
                    }
                }

                ProgressUpdated?.Invoke(this, $"تم العثور على {allFiles.Count} ملف للفحص");

                // حساب الهاش لكل ملف
                int processedFiles = 0;
                foreach (var file in allFiles)
                {
                    try
                    {
                        ProgressUpdated?.Invoke(this, $"معالجة الملف {processedFiles + 1} من {allFiles.Count}: {file.Name}");
                        
                        var hash = await CalculateFileHashAsync(file.FullName);
                        if (!string.IsNullOrEmpty(hash))
                        {
                            if (!fileHashMap.ContainsKey(hash))
                            {
                                fileHashMap[hash] = new List<FileInfo>();
                            }
                            fileHashMap[hash].Add(file);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"خطأ في معالجة الملف {file.FullName}: {ex.Message}");
                    }

                    processedFiles++;
                }

                // إنشاء مجموعات الملفات المكررة
                foreach (var kvp in fileHashMap.Where(x => x.Value.Count > 1))
                {
                    var group = new DuplicateFileGroup
                    {
                        Hash = kvp.Key,
                        Files = kvp.Value,
                        TotalSize = kvp.Value.Sum(f => f.Length),
                        WastedSpace = kvp.Value.Skip(1).Sum(f => f.Length) // كل الملفات عدا الأول
                    };
                    duplicateGroups.Add(group);
                }

                ProgressUpdated?.Invoke(this, $"تم العثور على {duplicateGroups.Count} مجموعة من الملفات المكررة");
            }
            catch (Exception ex)
            {
                ProgressUpdated?.Invoke(this, $"خطأ في البحث: {ex.Message}");
            }

            return duplicateGroups.OrderByDescending(g => g.WastedSpace).ToList();
        }

        private async Task<List<FileInfo>> GetFilesRecursivelyAsync(string directoryPath, long minFileSize)
        {
            var files = new List<FileInfo>();

            await Task.Run(() =>
            {
                try
                {
                    var directory = new DirectoryInfo(directoryPath);
                    GetFilesRecursively(directory, files, minFileSize);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في فحص المجلد {directoryPath}: {ex.Message}");
                }
            });

            return files;
        }

        private void GetFilesRecursively(DirectoryInfo directory, List<FileInfo> files, long minFileSize)
        {
            try
            {
                // تجنب مجلدات النظام الحساسة
                if (IsSystemDirectory(directory.FullName))
                    return;

                // إضافة الملفات من المجلد الحالي
                foreach (var file in directory.GetFiles())
                {
                    try
                    {
                        if (file.Length >= minFileSize && !IsSystemFile(file.FullName))
                        {
                            files.Add(file);
                        }
                    }
                    catch
                    {
                        // تجاهل الملفات المحمية
                    }
                }

                // فحص المجلدات الفرعية
                foreach (var subDirectory in directory.GetDirectories())
                {
                    try
                    {
                        GetFilesRecursively(subDirectory, files, minFileSize);
                    }
                    catch
                    {
                        // تجاهل المجلدات المحمية
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء الوصول
            }
        }

        private async Task<string> CalculateFileHashAsync(string filePath)
        {
            try
            {
                return await Task.Run(() =>
                {
                    using (var stream = File.OpenRead(filePath))
                    using (var sha256 = SHA256.Create())
                    {
                        var hash = sha256.ComputeHash(stream);
                        return Convert.ToBase64String(hash);
                    }
                });
            }
            catch
            {
                return string.Empty;
            }
        }

        private bool IsSystemDirectory(string path)
        {
            var systemDirs = new[]
            {
                "Windows", "System32", "SysWOW64", "Program Files", "Program Files (x86)",
                "ProgramData", "$Recycle.Bin", "System Volume Information"
            };

            return systemDirs.Any(sysDir => 
                path.Contains(Path.DirectorySeparatorChar + sysDir + Path.DirectorySeparatorChar) ||
                path.EndsWith(Path.DirectorySeparatorChar + sysDir));
        }

        private bool IsSystemFile(string filePath)
        {
            try
            {
                var fileName = Path.GetFileName(filePath).ToLower();
                var systemFiles = new[]
                {
                    "ntuser.dat", "pagefile.sys", "hiberfil.sys", "swapfile.sys",
                    "bootmgr", "bootmgfw.efi"
                };

                return systemFiles.Contains(fileName);
            }
            catch
            {
                return false;
            }
        }

        public async Task<CleaningResult> DeleteDuplicateFilesAsync(List<DuplicateFileGroup> groups, bool keepNewest = true)
        {
            var result = new CleaningResult
            {
                CleaningType = "حذف الملفات المكررة",
                CleaningDate = DateTime.Now
            };

            var startTime = DateTime.Now;
            long totalFilesDeleted = 0;
            long totalSpaceFreed = 0;

            try
            {
                foreach (var group in groups)
                {
                    ProgressUpdated?.Invoke(this, $"معالجة مجموعة الملفات المكررة...");

                    var filesToDelete = keepNewest 
                        ? group.Files.OrderBy(f => f.LastWriteTime).Take(group.Files.Count - 1)
                        : group.Files.Skip(1);

                    foreach (var file in filesToDelete)
                    {
                        try
                        {
                            var fileSize = file.Length;
                            File.Delete(file.FullName);
                            totalFilesDeleted++;
                            totalSpaceFreed += fileSize;
                        }
                        catch (Exception ex)
                        {
                            result.Errors.Add($"لا يمكن حذف الملف {file.FullName}: {ex.Message}");
                        }
                    }
                }

                result.TotalFilesDeleted = totalFilesDeleted;
                result.TotalSpaceFreed = totalSpaceFreed;
                result.CleaningDuration = DateTime.Now - startTime;
                result.IsSuccessful = result.Errors.Count == 0;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"خطأ عام في حذف الملفات المكررة: {ex.Message}");
                result.IsSuccessful = false;
            }

            return result;
        }
    }

    /// <summary>
    /// مجموعة الملفات المكررة
    /// </summary>
    public class DuplicateFileGroup
    {
        public string Hash { get; set; } = string.Empty;
        public List<FileInfo> Files { get; set; } = new List<FileInfo>();
        public long TotalSize { get; set; }
        public long WastedSpace { get; set; }

        public string GetFormattedWastedSpace()
        {
            if (WastedSpace < 1024)
                return $"{WastedSpace} B";
            else if (WastedSpace < 1024 * 1024)
                return $"{WastedSpace / 1024.0:F2} KB";
            else if (WastedSpace < 1024 * 1024 * 1024)
                return $"{WastedSpace / (1024.0 * 1024.0):F2} MB";
            else
                return $"{WastedSpace / (1024.0 * 1024.0 * 1024.0):F2} GB";
        }
    }
}
