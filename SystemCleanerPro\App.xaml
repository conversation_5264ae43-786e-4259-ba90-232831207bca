﻿<Application x:Class="SystemCleanerPro.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:SystemCleanerPro"
             xmlns:utilities="clr-namespace:SystemCleanerPro.Utilities"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Pink" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- المحولات -->
            <utilities:SpaceFormatterConverter x:Key="SpaceFormatterConverter" />
            <utilities:PercentageColorConverter x:Key="PercentageColorConverter" />
            <utilities:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

            <!-- الأنماط العامة -->
            <Style x:Key="TitleTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="24"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
            </Style>

            <Style x:Key="SubtitleTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="16"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
            </Style>

            <!-- أنماط الأزرار المخصصة -->
            <Style x:Key="CleanButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Height" Value="45"/>
                <Setter Property="Margin" Value="5"/>
                <Setter Property="FontSize" Value="13"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
            </Style>

            <!-- أنماط البطاقات -->
            <Style x:Key="InfoCardStyle" TargetType="materialDesign:Card">
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Padding" Value="16"/>
                <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>

            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
