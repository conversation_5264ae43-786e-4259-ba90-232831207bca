# دليل الاستخدام - System Cleaner Pro

## 🚀 البدء السريع

### تشغيل البرنامج
1. انقر بزر الماوس الأيمن على ملف `SystemCleanerPro.exe`
2. اختر "تشغيل كمدير" (Run as Administrator)
3. اقبل طلب UAC للحصول على صلاحيات المدير

### التنظيف السريع
1. انقر على زر **"تنظيف سريع"** في الواجهة الرئيسية
2. انتظر حتى انتهاء العملية
3. راجع التقرير المفصل في اللوحة اليمنى

## 🧹 أدوات التنظيف

### تنظيف الملفات المؤقتة
- **الوظيفة**: حذف ملفات Temp وملفات السجل القديمة
- **المواقع المشمولة**:
  - `%temp%` (مجلد المستخدم المؤقت)
  - `C:\Windows\Temp` (مجلد النظام المؤقت)
  - `C:\Windows\Prefetch` (ملفات التحميل المسبق)
  - ملفات السجل (.log, .tmp, .bak) من جميع الأقراص

### تحسين الذاكرة
- **الوظيفة**: تحرير الذاكرة غير المستخدمة
- **الطريقة**: استخدام `EmptyWorkingSet()` API
- **الأمان**: لا يؤثر على العمليات الحرجة للنظام

### تنظيف السجل
- **الوظيفة**: حذف المفاتيح التالفة والميتة
- **الأمان**: إنشاء نقطة استعادة تلقائية قبل التنظيف
- **المواقع المشمولة**:
  - مفاتيح البرامج المحذوفة
  - CLSID غير صالحة
  - مسارات DLL المفقودة

### تنظيف المتصفحات
- **المتصفحات المدعومة**:
  - Google Chrome
  - Microsoft Edge
  - Mozilla Firefox
  - Opera
- **البيانات المنظفة**:
  - ملفات الكاش
  - التخزين المحلي
  - (اختياري) الكوكيز والتاريخ

## ⚙️ الأدوات المتقدمة

### مدير برامج الإقلاع
1. انقر على **"إدارة برامج الإقلاع"**
2. راجع قائمة البرامج التي تعمل عند بدء التشغيل
3. استخدم المفتاح لتفعيل/تعطيل البرامج
4. انقر **"حفظ"** لتطبيق التغييرات

### باحث الملفات المكررة
1. انقر على **"البحث عن المكررات"**
2. اختر المجلدات للبحث فيها
3. حدد الحد الأدنى لحجم الملف
4. انتظر انتهاء عملية المسح
5. راجع النتائج واختر الملفات للحذف

### محلل القرص الصلب
1. انقر على **"تحليل القرص"**
2. اختر القرص للتحليل
3. راجع الرسوم البيانية لاستخدام المساحة
4. اعرض أكبر الملفات والمجلدات

## 📊 مراقبة الأداء

### المؤشرات الحية
- **استخدام المعالج**: نسبة مئوية حية
- **استخدام الذاكرة**: نسبة مئوية حية
- **نقاط النظام**: تقييم من 0-100

### التنبيهات
- تنبيه عند تجاوز استخدام المعالج 80%
- تنبيه عند تجاوز استخدام الذاكرة 85%
- يمكن تخصيص النسب من الإعدادات

## ⏰ الجدولة التلقائية

### إعداد التنظيف التلقائي
1. افتح **الإعدادات**
2. انتقل إلى قسم **"التنظيف التلقائي"**
3. فعّل **"تفعيل التنظيف التلقائي"**
4. اختر التكرار:
   - كل 5 دقائق
   - كل 30 دقيقة
   - كل ساعة
   - يومياً
   - أسبوعياً
   - مخصص (بالدقائق)

### خيارات التنظيف التلقائي
- ✅ تنظيف الملفات المؤقتة
- ✅ تحسين الذاكرة
- ⚠️ تنظيف السجل (غير مُوصى به للتشغيل التلقائي)
- ✅ الوضع الصامت (بدون إشعارات)

## 🎨 تخصيص الواجهة

### تغيير اللغة
1. انقر على أيقونة الترجمة في شريط العنوان
2. أو افتح الإعدادات واختر اللغة المطلوبة
3. أعد تشغيل البرنامج لتطبيق التغيير

### تغيير الثيم
1. انقر على أيقونة الثيم في شريط العنوان
2. أو افتح الإعدادات واختر الثيم المطلوب:
   - فاتح (Light)
   - داكن (Dark)

## 📋 قراءة التقارير

### تقرير التنظيف
- **نوع التنظيف**: العملية المنفذة
- **عدد الملفات المحذوفة**: إجمالي الملفات
- **المساحة المحررة**: بالبايت/KB/MB/GB
- **مدة التنظيف**: الوقت المستغرق
- **المواقع المنظفة**: قائمة المجلدات

### حالة النظام
- **اسم الحاسوب**: اسم الجهاز
- **إصدار النظام**: Windows version
- **نقاط النظام**: تقييم الأداء الحالي

## ⚠️ نصائح مهمة للأمان

### قبل التنظيف
1. **أغلق جميع البرامج المهمة**
2. **احفظ عملك** في جميع التطبيقات
3. **تأكد من وجود نسخة احتياطية** للبيانات المهمة

### أثناء التنظيف
1. **لا تقم بإيقاف البرنامج** أثناء التنظيف
2. **لا تقم بإيقاف تشغيل الحاسوب**
3. **انتظر انتهاء العملية** بالكامل

### بعد التنظيف
1. **راجع التقرير** للتأكد من نجاح العملية
2. **أعد تشغيل الحاسوب** إذا لزم الأمر
3. **تحقق من عمل البرامج** بشكل طبيعي

## 🔧 حل المشاكل الشائعة

### البرنامج لا يعمل
- تأكد من تشغيله كمدير
- تأكد من وجود .NET 8.0 Runtime
- تحقق من مكافح الفيروسات

### التنظيف بطيء
- أغلق البرامج الأخرى
- تأكد من وجود مساحة كافية على القرص
- انتظر انتهاء عمليات النظام الأخرى

### خطأ في الوصول
- تأكد من صلاحيات المدير
- أغلق البرامج التي تستخدم الملفات
- أعد تشغيل الحاسوب وحاول مرة أخرى

### لا تظهر بعض الملفات
- بعض الملفات محمية من النظام
- بعض المجلدات تتطلب صلاحيات خاصة
- هذا سلوك طبيعي للحماية

## 📞 الحصول على المساعدة

إذا واجهت أي مشاكل:
1. راجع هذا الدليل أولاً
2. تحقق من ملف السجل `SystemCleanerPro.log`
3. تواصل مع الدعم الفني
4. أبلغ عن الأخطاء في GitHub Issues

---

**تذكر**: استخدم البرنامج بحذر واحتفظ بنسخ احتياطية من البيانات المهمة!
