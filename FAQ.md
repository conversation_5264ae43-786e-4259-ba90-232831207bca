# الأسئلة الشائعة - Frequently Asked Questions

## 🔧 التثبيت والتشغيل

### س: هل البرنامج مجاني؟
**ج:** نعم، البرنامج مجاني تماماً ومفتوح المصدر تحت رخصة MIT.

### س: لماذا يطلب البرنامج صلاحيات المدير؟
**ج:** البرنامج يحتاج صلاحيات المدير للوصول إلى:
- مجلدات النظام المحمية
- سجل Windows (Registry)
- ملفات العمليات الأخرى
- إنشاء نقاط الاستعادة

### س: هل البرنامج آمن؟
**ج:** نعم، البرنامج آمن تماماً. الكود مفتوح المصدر ويمكن مراجعته. قد تظهر تحذيرات من مكافح الفيروسات بسبب طبيعة عمل البرنامج (تنظيف النظام).

### س: ما هي أنظمة التشغيل المدعومة؟
**ج:** البرنامج يدعم:
- Windows 7 SP1 أو أحدث
- Windows 8/8.1
- Windows 10
- Windows 11
- Windows Server 2012 أو أحدث

## 🧹 التنظيف والأداء

### س: هل التنظيف آمن؟
**ج:** نعم، البرنامج مصمم ليكون آمناً:
- يتجنب ملفات النظام الحرجة
- ينشئ نقاط استعادة قبل تنظيف السجل
- يحذف فقط الملفات المؤقتة والآمنة

### س: ماذا يحدث إذا حذف البرنامج ملفاً مهماً؟
**ج:** البرنامج مصمم لتجنب هذا، لكن:
- يمكن استعادة الملفات من سلة المحذوفات
- نقاط الاستعادة تساعد في استرجاع حالة النظام
- احتفظ بنسخ احتياطية من البيانات المهمة

### س: لماذا لا يحذف البرنامج بعض الملفات؟
**ج:** قد يكون السبب:
- الملف مستخدم من برنامج آخر
- الملف محمي من النظام
- عدم وجود صلاحيات كافية
- الملف في مجلد محمي

### س: كم مرة يجب تشغيل التنظيف؟
**ج:** يعتمد على الاستخدام:
- الاستخدام العادي: أسبوعياً
- الاستخدام الكثيف: يومياً
- يمكن تفعيل التنظيف التلقائي

## ⚙️ الإعدادات والتخصيص

### س: كيف أغير لغة البرنامج؟
**ج:** يمكنك تغيير اللغة من:
- أيقونة الترجمة في شريط العنوان
- الإعدادات > الإعدادات العامة > اللغة
- إعادة تشغيل البرنامج مطلوبة

### س: كيف أفعل الوضع الليلي؟
**ج:** من الإعدادات > الإعدادات العامة > الثيم، أو انقر على أيقونة الثيم في شريط العنوان.

### س: كيف أعطل التنظيف التلقائي؟
**ج:** من الإعدادات > التنظيف التلقائي > قم بإلغاء تفعيل "تفعيل التنظيف التلقائي".

## 🔍 الميزات المتقدمة

### س: ما هو نظام نقاط النظام؟
**ج:** نظام تقييم من 0-100 يعتمد على:
- استخدام المعالج
- استخدام الذاكرة
- حالة النظام العامة
- كلما زادت النقاط، كان النظام أفضل

### س: كيف يعمل باحث الملفات المكررة؟
**ج:** يستخدم خوارزمية SHA256 لحساب hash لكل ملف ومقارنة النتائج للعثور على الملفات المتطابقة تماماً.

### س: هل يمكن استعادة برامج الإقلاع المعطلة؟
**ج:** نعم، من مدير برامج الإقلاع يمكنك إعادة تفعيل أي برنامج تم تعطيله.

## ❗ المشاكل والحلول

### س: البرنامج لا يعمل على Windows 7
**ج:** تأكد من:
- تثبيت .NET 8.0 Runtime
- تحديث Windows 7 إلى SP1
- تثبيت تحديثات Windows الأساسية

### س: البرنامج بطيء جداً
**ج:** جرب:
- إغلاق البرامج الأخرى
- تشغيل فحص القرص (chkdsk)
- تأكد من وجود مساحة كافية (10% على الأقل)
- إعادة تشغيل الحاسوب

### س: مكافح الفيروسات يحذف البرنامج
**ج:** هذا تحذير خاطئ (false positive). أضف البرنامج إلى قائمة الاستثناءات في مكافح الفيروسات.

### س: "خطأ في الوصول مرفوض"
**ج:** 
- تأكد من تشغيل البرنامج كمدير
- أغلق البرامج التي قد تستخدم نفس الملفات
- أعد تشغيل الحاسوب وحاول مرة أخرى

## 🔄 التحديث والصيانة

### س: كيف أعرف إذا كان هناك تحديث جديد؟
**ج:** حالياً يجب التحقق يدوياً من صفحة GitHub. نظام التحديث التلقائي قادم في الإصدارات القادمة.

### س: هل سأفقد إعداداتي عند التحديث؟
**ج:** لا، الإعدادات محفوظة في ملفات منفصلة ولن تتأثر بالتحديث.

### س: كيف أنسخ إعداداتي إلى جهاز آخر؟
**ج:** انسخ ملفات الإعدادات من مجلد `%APPDATA%\SystemCleanerPro\` إلى نفس المجلد في الجهاز الآخر.

## 📊 الأداء والإحصائيات

### س: ما معنى الألوان في مؤشرات الأداء؟
**ج:**
- **أخضر**: أداء ممتاز (أقل من 50%)
- **برتقالي**: أداء متوسط (50-75%)
- **أحمر**: أداء ضعيف (أكثر من 75%)

### س: لماذا لا تتطابق إحصائيات البرنامج مع مدير المهام؟
**ج:** قد يكون السبب:
- طرق حساب مختلفة
- توقيت مختلف للقراءة
- البرنامج يعرض متوسط فترة زمنية

## 🛠️ التخصيص والتطوير

### س: هل يمكنني تعديل البرنامج؟
**ج:** نعم، البرنامج مفتوح المصدر. يمكنك:
- تحميل الكود المصدري
- تعديله حسب احتياجاتك
- المساهمة في التطوير

### س: كيف أبلغ عن خطأ أو أقترح ميزة؟
**ج:** من خلال:
- GitHub Issues للأخطاء والاقتراحات
- البريد الإلكتروني للدعم
- المناقشات في GitHub Discussions

### س: هل يمكن إضافة دعم لغات أخرى؟
**ج:** نعم، البرنامج مصمم لدعم لغات متعددة. يمكن إضافة ملفات ترجمة جديدة.

## 🔒 الخصوصية والأمان

### س: هل البرنامج يجمع بيانات شخصية؟
**ج:** لا، البرنامج لا يجمع أو يرسل أي بيانات شخصية. كل شيء يحدث محلياً على جهازك.

### س: هل البرنامج يتصل بالإنترنت؟
**ج:** حالياً لا، لكن الإصدارات القادمة قد تتضمن:
- فحص التحديثات
- تحميل قواعد بيانات التنظيف
- (كل شيء اختياري ومع إذن المستخدم)

### س: هل يمكن استخدام البرنامج في بيئة الشركات؟
**ج:** نعم، البرنامج مناسب للاستخدام التجاري تحت رخصة MIT.

---

## 📞 لم تجد إجابة لسؤالك؟

إذا لم تجد إجابة لسؤالك هنا:
1. ابحث في [GitHub Issues](https://github.com/your-repo/SystemCleanerPro/issues)
2. راجع [دليل الاستخدام](USAGE.md)
3. راجع [دليل التثبيت](INSTALL.md)
4. أنشئ issue جديد مع تفاصيل سؤالك

**تذكر**: قدم أكبر قدر من التفاصيل عند طرح سؤال جديد (نظام التشغيل، إصدار البرنامج، رسالة الخطأ، إلخ).
