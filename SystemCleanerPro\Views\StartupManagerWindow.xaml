<Window x:Class="SystemCleanerPro.Views.StartupManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:models="clr-namespace:SystemCleanerPro.Models"
        Title="مدير برامج الإقلاع - Startup Manager" 
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- شريط العنوان -->
        <materialDesign:Card Grid.Row="0" Margin="10" Padding="15" 
                            materialDesign:ShadowAssist.ShadowDepth="Depth2">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <materialDesign:PackIcon Grid.Column="0" Kind="Rocket" 
                                       Width="32" Height="32" 
                                       Foreground="{StaticResource PrimaryBrush}" 
                                       VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Margin="15,0,0,0" VerticalAlignment="Center">
                    <TextBlock Text="مدير برامج الإقلاع" 
                              FontSize="18" FontWeight="Bold"/>
                    <TextBlock Text="إدارة البرامج التي تعمل عند بدء تشغيل النظام" 
                              FontSize="12" 
                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>

                <Button Grid.Column="2" 
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Content="تحديث القائمة"
                        Command="{Binding RefreshCommand}"/>
            </Grid>
        </materialDesign:Card>

        <!-- قائمة برامج الإقلاع -->
        <materialDesign:Card Grid.Row="1" Margin="10" Padding="0" 
                            materialDesign:ShadowAssist.ShadowDepth="Depth2">
            <DataGrid ItemsSource="{Binding StartupItems}"
                      SelectedItem="{Binding SelectedStartupItem}"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      SelectionMode="Single"
                      materialDesign:DataGridAssist.CellPadding="8"
                      materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                
                <DataGrid.Columns>
                    <!-- حالة التفعيل -->
                    <DataGridTemplateColumn Header="الحالة" Width="80">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <ToggleButton IsChecked="{Binding IsEnabled, Mode=TwoWay}"
                                            Style="{StaticResource MaterialDesignSwitchToggleButton}"
                                            Command="{Binding DataContext.ToggleStartupItemCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                            CommandParameter="{Binding}"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- اسم البرنامج -->
                    <DataGridTextColumn Header="اسم البرنامج" 
                                       Binding="{Binding Name}" 
                                       Width="200"/>

                    <!-- الناشر -->
                    <DataGridTextColumn Header="الناشر" 
                                       Binding="{Binding Publisher}" 
                                       Width="150"/>

                    <!-- المسار -->
                    <DataGridTextColumn Header="المسار" 
                                       Binding="{Binding Path}" 
                                       Width="*"/>

                    <!-- الموقع -->
                    <DataGridTextColumn Header="الموقع" 
                                       Binding="{Binding Location}" 
                                       Width="200"/>

                    <!-- النوع -->
                    <DataGridTemplateColumn Header="النوع" Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Width="16" Height="16" 
                                                           VerticalAlignment="Center" 
                                                           Margin="0,0,5,0">
                                        <materialDesign:PackIcon.Style>
                                            <Style TargetType="materialDesign:PackIcon">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Type}" Value="Registry">
                                                        <Setter Property="Kind" Value="Registry"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Type}" Value="StartupFolder">
                                                        <Setter Property="Kind" Value="Folder"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Type}" Value="Service">
                                                        <Setter Property="Kind" Value="Cog"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Type}" Value="Task">
                                                        <Setter Property="Kind" Value="ClockOutline"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </materialDesign:PackIcon.Style>
                                    </materialDesign:PackIcon>
                                    <TextBlock Text="{Binding Type}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>

        <!-- شريط الأدوات السفلي -->
        <materialDesign:Card Grid.Row="2" Margin="10" Padding="15" 
                            materialDesign:ShadowAssist.ShadowDepth="Depth1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Information" 
                                           Width="16" Height="16" 
                                           Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding StatusMessage}" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="تفعيل المحدد"
                            Command="{Binding EnableSelectedCommand}"
                            IsEnabled="{Binding HasSelectedItem}"
                            Margin="5,0"/>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="تعطيل المحدد"
                            Command="{Binding DisableSelectedCommand}"
                            IsEnabled="{Binding HasSelectedItem}"
                            Margin="5,0"/>
                    
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Content="إغلاق"
                            Command="{Binding CloseCommand}"
                            Margin="5,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Window>
